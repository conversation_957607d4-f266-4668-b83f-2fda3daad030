<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伟创力电子智能工厂建设 | 智能制造案例 | 制造业数字化转型专家</title>
    <meta name="description" content="探索伟创力电子智能工厂建设案例，了解我们如何通过数字孪生系统和IoT平台帮助伟创力电子实现设备智能化监控与预测性维护。">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="../js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
    </style>
    <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:5356023,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 案例头部 -->
    <section class="bg-blue-800 pt-24 pb-12 text-white">
        <div class="container mx-auto px-4">
            <div class="flex items-center mb-4">
                <a href="../cases.html" class="text-blue-200 hover:text-white flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    返回案例列表
                </a>
            </div>
            <div class="flex items-center mb-6">
                <span class="bg-blue-700 text-white text-sm font-medium px-3 py-1 rounded-full mr-2">电子制造</span>
                <span class="bg-green-600 text-white text-sm font-medium px-3 py-1 rounded-full mr-2">数字孪生</span>
                <span class="bg-purple-600 text-white text-sm font-medium px-3 py-1 rounded-full">IoT平台</span>
            </div>
            <h1 class="text-4xl font-bold mb-4">伟创力电子智能工厂建设</h1>
            <p class="text-xl max-w-3xl">伟创力电子苏州工厂通过部署我们的数字孪生系统和IoT平台，实现了生产设备和工艺的可视化监控、预测性维护和虚拟仿真，显著提升了设备利用率和生产效率。</p>
        </div>
    </section>

    <!-- 案例概述 -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <!-- 主要内容 -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
                <div class="p-8">
                    <h2 class="text-3xl font-bold mb-6">客户概述</h2>
                    <p class="text-gray-700 mb-6">
                        伟创力电子是全球领先的电子制造服务供应商，在全球多个国家拥有工厂。其苏州工厂主要生产高端消费电子产品和工业电子设备，年产量超过2000万台，拥有20多条自动化生产线和近5000名员工。
                        随着消费电子产品生命周期缩短和定制化需求增加，工厂面临着提高生产柔性、缩短产品上市时间、提高设备利用率以及降低运营成本等挑战。
                    </p>

                    <div class="flex flex-col md:flex-row mb-8">
                        <div class="md:w-1/2 md:pr-4 mb-4 md:mb-0">
                            <h3 class="text-xl font-bold mb-4">面临挑战</h3>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>设备状态监控手段落后，无法实时掌握</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>设备故障率高，计划外停机时间长</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>产品类型多变，生产切换时间长</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>生产线能耗高，能源管理粗放</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>设备维护被动，缺乏数据支持的科学决策</span>
                                </li>
                            </ul>
                        </div>
                        <div class="md:w-1/2 md:pl-4">
                            <h3 class="text-xl font-bold mb-4">项目目标</h3>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>实现设备全生命周期数字化管理</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>构建设备预测性维护系统，降低故障率</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>提高工厂设备整体效率(OEE)</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>建立精细化能源管理系统</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="h-5 w-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>实现产线柔性化快速切换</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold text-blue-800 mb-2">62%</div>
                            <p class="text-gray-600">计划外停机时间减少</p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold text-blue-800 mb-2">85%</div>
                            <p class="text-gray-600">设备故障预测准确率</p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold text-blue-800 mb-2">18%</div>
                            <p class="text-gray-600">能源消耗降低</p>
                        </div>
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold text-blue-800 mb-2">25%</div>
                            <p class="text-gray-600">产线切换时间缩短</p>
                        </div>
                    </div>

                    <img src="../images/cases/flextronics.jpg" alt="伟创力电子智能工厂" class="w-full rounded-lg shadow-md mb-6">
                </div>
            </div>

            <!-- 解决方案 -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
                <div class="p-8">
                    <h2 class="text-3xl font-bold mb-6">解决方案实施</h2>
                    <p class="text-gray-700 mb-8">
                        针对伟创力电子苏州工厂的需求，我们设计并实施了基于数字孪生技术和工业物联网平台的智能工厂解决方案，全面提升工厂的设备管理和生产效率。
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
                        <div>
                            <h3 class="text-xl font-bold mb-4 flex items-center">
                                <div class="bg-blue-100 rounded-full p-2 mr-3">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    </svg>
                                </div>
                                设备数字孪生系统
                            </h3>
                            <ul class="ml-12 space-y-2 list-disc text-gray-700">
                                <li>为关键设备创建数字孪生模型，实现虚实映射</li>
                                <li>实时监控设备运行状态、参数和性能</li>
                                <li>设备3D可视化展示与远程操控</li>
                                <li>历史运行数据分析与设备健康评估</li>
                                <li>基于数字孪生的设备优化与仿真</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-4 flex items-center">
                                <div class="bg-blue-100 rounded-full p-2 mr-3">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                预测性维护系统
                            </h3>
                            <ul class="ml-12 space-y-2 list-disc text-gray-700">
                                <li>设备多维度数据采集与边缘计算处理</li>
                                <li>基于机器学习的故障预测模型</li>
                                <li>设备健康状态实时评估</li>
                                <li>智能维护计划生成与任务分配</li>
                                <li>维护知识库与最佳实践分享平台</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-4 flex items-center">
                                <div class="bg-blue-100 rounded-full p-2 mr-3">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                工业物联网平台
                            </h3>
                            <ul class="ml-12 space-y-2 list-disc text-gray-700">
                                <li>异构设备统一接入与数据标准化</li>
                                <li>边缘计算网关实现本地数据处理</li>
                                <li>工业协议转换与数据安全加密</li>
                                <li>设备、产线与工厂级数据集成</li>
                                <li>可扩展的物联网架构，支持快速接入新设备</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-4 flex items-center">
                                <div class="bg-blue-100 rounded-full p-2 mr-3">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                智能能源管理
                            </h3>
                            <ul class="ml-12 space-y-2 list-disc text-gray-700">
                                <li>设备级能耗监测与分析</li>
                                <li>能源使用可视化与实时监控</li>
                                <li>能源消耗异常检测与预警</li>
                                <li>基于AI的能源优化建议</li>
                                <li>能源绩效评估与优化方案</li>
                            </ul>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <img src="../images/cases/flextronics-detail1.jpg" alt="伟创力数字孪生系统" class="rounded-lg shadow-md">
                        <img src="../images/cases/flextronics-detail2.jpg" alt="伟创力设备监控中心" class="rounded-lg shadow-md">
                    </div>
                </div>
            </div>

            <!-- 客户评价 -->
            <div class="bg-blue-800 text-white rounded-lg shadow-lg p-8">
                <div class="flex items-center mb-6">
                    <div class="text-4xl mr-4">❝</div>
                    <div class="flex-1">
                        <p class="text-lg italic mb-6">
                            奥斯坦丁的数字孪生与IoT平台彻底改变了我们工厂的设备管理方式。通过预测性维护，我们将计划外停机时间减少了62%，大大提高了生产线运行效率。能源管理系统帮助我们识别了多个能源浪费点，整体能耗降低了18%。设备数字孪生系统不仅实现了设备的可视化管理，还帮助我们优化了设备参数配置，提高了产品质量稳定性。这些技术为我们打造了真正意义上的智能工厂。
                        </p>
                    </div>
                </div>
                <div class="font-bold text-lg">Mark Johnson</div>
                <div class="text-blue-200">伟创力电子苏州工厂 | 运营总监</div>
            </div>
        </div>
    </section>

    <!-- 相关案例 -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl font-bold mb-8">相关案例</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="../images/cases/sharp.jpg" alt="无锡夏普智能制造数字化转型" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="text-xl font-bold mb-2">无锡夏普智能制造数字化转型</h3>
                        <p class="text-gray-600 mb-4">夏普集团通过MES+WMS集成解决方案实现全流程数字化管控。</p>
                        <a href="case1.html" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="../images/cases/northland.jpg" alt="北陆微电子整厂数字化改造" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="text-xl font-bold mb-2">北陆微电子整厂数字化改造</h3>
                        <p class="text-gray-600 mb-4">北陆微电子通过MES+WMS集成解决方案实现了全流程数字化管理。</p>
                        <a href="case3.html" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="../images/cases/xiaomi.jpg" alt="小米手机标杆工厂建设" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="text-xl font-bold mb-2">小米手机标杆工厂建设</h3>
                        <p class="text-gray-600 mb-4">小米集团通过实施MES+DMS一体化解决方案，构建了柔性化生产体系。</p>
                        <a href="case4.html" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 咨询入口 -->
    <section class="py-16 bg-blue-800 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">准备开启您的数字化转型之旅？</h2>
            <p class="max-w-3xl mx-auto mb-8 text-lg">我们的专业团队将为您提供一对一咨询，详细了解您的需求，并提供定制化解决方案。</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="../contact.html" class="bg-white text-blue-800 px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition duration-300">联系我们</a>
                <a href="../contact.html#demo" class="border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-blue-800 transition duration-300">预约演示</a>
            </div>
        </div>
    </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="../js/components.js?v=1.0.1"></script>
</body>
</html> 