<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源中心 | 制造业数字化转型专家</title>
    <meta name="description" content="获取制造业数字化转型的最新资讯、白皮书、案例研究和技术指南，助力企业智能制造发展。">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }

        /* 资源列表容器样式 */
        .resources-grid {
            position: relative;
            overflow: hidden;
            min-height: 400px;
            margin-bottom: 2rem;
        }

        /* 资源列表样式 */
        .resources-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            position: relative;
            width: 100%;
        }

        /* 资源项样式 */
        .resource-item {
            display: none;
        }

        .resource-item.active {
            display: block;
        }

        @media (max-width: 1024px) {
            .resources-list {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .resources-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
        <script>
        <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:5356023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 主标题 -->
    <section class="bg-blue-800 pt-16 pb-8 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-2">资源中心</h1>
            <p class="text-xl max-w-3xl">获取智能制造最新白皮书、案例研究、技术文档和视频教程，助力您的数字化转型之旅</p>
        </div>
    </section>

    <!-- 资源导航 -->
    <section class="bg-white py-6 shadow-md sticky top-16 z-10">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap -mx-2">
                <a href="#all" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-800 text-white hover:bg-blue-700 transition duration-300 active" data-filter="all">所有资源</a>
                <a href="#whitepaper" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300" data-filter="whitepaper">白皮书</a>
                <a href="#case" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300" data-filter="case">案例研究</a>
                <a href="#technical" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300" data-filter="technical">技术文档</a>
                <a href="#video" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300" data-filter="video">视频教程</a>
                <a href="#infographic" class="resource-filter px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300" data-filter="infographic">信息图表</a>
            </div>
        </div>
    </section>

    <!-- 热门资源 -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold mb-12 text-center">热门推荐资源</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <!-- 热门资源1 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1">
                    <img src="images/resources/whitepaper1.jpg" alt="智能制造白皮书" class="w-full h-48 object-cover">
                    <div class="p-5">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">白皮书</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">智能制造系统实施白皮书</h3>
                        <p class="text-gray-600 text-sm mb-3">全面介绍智能制造系统的核心组件和成功实施策略</p>
                        <a href="#" class="text-blue-800 font-medium text-sm hover:text-blue-700">立即下载</a>
                    </div>
                </div>
                
                <!-- 热门资源2 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1">
                    <img src="images/resources/guide1.jpg" alt="MES实施指南" class="w-full h-48 object-cover">
                    <div class="p-5">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">实施指南</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">MES系统建设与实施指南</h3>
                        <p class="text-gray-600 text-sm mb-3">工厂MES系统选型、建设和实施全流程解析</p>
                        <a href="#" class="text-blue-800 font-medium text-sm hover:text-blue-700">立即下载</a>
                    </div>
                </div>
                
                <!-- 热门资源3 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1">
                    <div class="relative">
                        <img src="images/resources/video1.jpg" alt="数字工厂视频" class="w-full h-48 object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white opacity-80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="p-5">
                        <div class="flex items-center mb-2">
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">视频</span>
                        </div>
                        <h3 class="text-lg font-bold mb-2">数字工厂解决方案介绍</h3>
                        <p class="text-gray-600 text-sm mb-3">数字工厂整体架构和主要功能模块详细介绍</p>
                        <a href="#" class="text-blue-800 font-medium text-sm hover:text-blue-700">观看视频</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 资源列表 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold mb-2 text-center">全部资源</h2>
            <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto">浏览我们的完整资源库，找到适合您业务需求的智能制造知识内容</p>
            
            <div class="resources-grid">
                <div class="resources-list">
                    <!-- 白皮书1 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="whitepaper">
                        <img src="images/resources/whitepaper2.jpg" alt="中国制造2025路线图" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">白皮书</span>
                                <span class="ml-2 text-sm text-gray-500">2023年04月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">《中国制造2025》政策解读与应对策略</h3>
                            <p class="text-gray-600 mb-4">详细解读国家智能制造政策方向，提供企业转型路线图和落地建议。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                立即下载
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- 案例研究1 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="case">
                        <img src="images/resources/case1.jpg" alt="汽车制造商案例" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">案例研究</span>
                                <span class="ml-2 text-sm text-gray-500">2023年01月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">某知名汽车制造商数字化转型案例</h3>
                            <p class="text-gray-600 mb-4">全面分析大型汽车制造企业如何通过智能制造系统提升产能30%，降低成本20%。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                阅读案例
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- 技术文档1 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="technical">
                        <img src="images/resources/tech1.jpg" alt="MES技术规范" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">技术文档</span>
                                <span class="ml-2 text-sm text-gray-500">2023年02月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">MES系统技术规范与架构设计指南</h3>
                            <p class="text-gray-600 mb-4">详细介绍MES系统的功能模块、技术架构、数据模型和接口规范。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                立即下载
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- 视频教程1 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="video">
                        <div class="relative">
                            <img src="images/resources/video2.jpg" alt="WMS视频教程" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white opacity-80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-red-100 text-red-800 text-xs px-3 py-1 rounded-full">视频教程</span>
                                <span class="ml-2 text-sm text-gray-500">2023年03月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">WMS系统功能与操作培训</h3>
                            <p class="text-gray-600 mb-4">详细讲解仓库管理系统的核心功能和日常操作流程，适合企业内部培训使用。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                观看视频
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- 信息图表1 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="infographic">
                        <img src="images/resources/infographic1.jpg" alt="数字孪生信息图" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-purple-100 text-purple-800 text-xs px-3 py-1 rounded-full">信息图表</span>
                                <span class="ml-2 text-sm text-gray-500">2023年02月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">数字孪生技术在制造业中的应用图解</h3>
                            <p class="text-gray-600 mb-4">直观展示数字孪生技术的工作原理、应用场景和实施步骤。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                查看图表
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- 白皮书2 -->
                    <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="whitepaper">
                        <img src="images/resources/whitepaper3.jpg" alt="人工智能在制造业的应用" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">白皮书</span>
                                <span class="ml-2 text-sm text-gray-500">2023年06月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">人工智能在智能制造中的应用与趋势</h3>
                            <p class="text-gray-600 mb-4">探讨AI技术如何赋能制造业，包括预测性维护、质量控制和自动化决策系统。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                立即下载
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 案例研究2 -->
                    <!-- <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="case">
                        <img src="images/resources/case2.jpg" alt="电子制造案例" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">案例研究</span>
                                <span class="ml-2 text-sm text-gray-500">2023年05月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">某电子制造企业智能制造转型实践</h3>
                            <p class="text-gray-600 mb-4">深入分析电子制造企业如何通过MES系统实现生产透明化，提升生产效率25%，降低不良品率18%。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                阅读案例
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </a>
                        </div>
                    </div> -->

                    <!-- 技术文档2 -->
                    <!-- <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1" data-type="technical">
                        <img src="images/resources/tech2.jpg" alt="WMS技术规范" class="w-full h-48 object-cover">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">技术文档</span>
                                <span class="ml-2 text-sm text-gray-500">2023年04月</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">WMS系统技术规范与接口设计指南</h3>
                            <p class="text-gray-600 mb-4">详细介绍WMS系统的功能模块、技术架构、数据模型和接口规范，包括与ERP、MES等系统的集成方案。</p>
                            <a href="#" class="text-blue-800 font-medium inline-flex items-center hover:text-blue-700">
                                立即下载
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        </div>
                    </div> -->
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-center mt-4">
                <nav class="inline-flex rounded-md shadow">
                    <button onclick="changePage('prev')" class="py-3 px-6 bg-white border border-gray-300 text-sm font-medium rounded-l-md text-gray-500 hover:bg-gray-50 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        上一页
                    </button>
                    <button onclick="changePage('next')" class="py-3 px-6 bg-white border border-gray-300 text-sm font-medium rounded-r-md text-gray-700 hover:bg-gray-50 flex items-center">
                        下一页
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </nav>
            </div>
        </div>
    </section>

    <!-- 资源订阅 -->
    <section class="py-16 bg-blue-800 text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-4">订阅资源更新</h2>
                <p class="mb-8">定期获取最新的智能制造白皮书、案例研究和技术资料，助力您的企业数字化转型</p>
                <form class="flex flex-col md:flex-row gap-4 justify-center">
                    <input type="email" placeholder="请输入您的邮箱地址" class="px-5 py-3 rounded-md flex-grow max-w-md text-gray-800" required>
                    <button type="submit" class="bg-white text-blue-800 font-medium px-6 py-3 rounded-md hover:bg-gray-100 transition-colors">
                        立即订阅
                    </button>
                </form>
                <p class="mt-4 text-sm text-blue-100">我们尊重您的隐私，不会向第三方分享您的信息</p>
            </div>
        </div>
    </section>

    <!-- 预约演示 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">开启您的智能制造之旅</h2>
            <p class="text-gray-600 mb-8 max-w-2xl mx-auto">通过我们的解决方案，提升生产效率，降低运营成本，加速数字化转型</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="contact.html" class="bg-blue-800 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium">联系我们</a>
                <a href="contact.html#demo" class="bg-white text-blue-800 border border-blue-800 px-6 py-3 rounded-md hover:bg-blue-50 transition-colors font-medium">预约演示</a>
            </div>
        </div>
    </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 聊天按钮 -->
    <div class="fixed bottom-6 right-6 z-40">
        <button id="chatButton" class="bg-blue-800 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:bg-blue-700 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
        </button>
    </div>

    <!-- 组件加载脚本 -->
    <script src="js/components.js?v=1.0.1"></script>
    <script>
        // 分页功能
        let currentPage = 1;
        let itemsPerPage = 3;

        // 根据屏幕大小调整每页显示数量
        function updateItemsPerPage() {
            if (window.innerWidth <= 768) {
                itemsPerPage = 3; // 移动设备每页显示3个
            } else if (window.innerWidth <= 1024) {
                itemsPerPage = 4; // 平板设备每页显示4个
            } else {
                itemsPerPage = 3; // 桌面设备每页显示3个
            }
            // 重新加载当前页
            if(document.readyState === 'complete') {
                changePage('current');
            }
        }

        // 页面加载和窗口调整时更新每页显示数量
        window.addEventListener('load', updateItemsPerPage);
        window.addEventListener('resize', updateItemsPerPage);

        function changePage(page) {
            let newPage = currentPage;
            if (page === 'prev' && currentPage > 1) {
                newPage = currentPage - 1;
            } else if (page === 'next' && currentPage < getTotalPages()) {
                newPage = currentPage + 1;
            } else if (page === 'current') {
                // 保持当前页不变，仅更新显示
            } else {
                return;
            }

            if (newPage === currentPage && page !== 'current') return;

            const visibleItems = getVisibleItems();
            const startIndex = (newPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            // 隐藏所有项目
            visibleItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示当前页的项目
            visibleItems.forEach((item, index) => {
                if (index >= startIndex && index < endIndex) {
                    item.classList.add('active');
                }
            });

            currentPage = newPage;
            updatePaginationButtons();
        }

        function getVisibleItems() {
            return Array.from(document.querySelectorAll('.resource-item')).filter(item => {
                return item.style.display !== 'none';
            });
        }

        function getTotalPages() {
            const visibleItems = getVisibleItems();
            return Math.ceil(visibleItems.length / itemsPerPage);
        }

        function updatePaginationButtons() {
            const prevButton = document.querySelector('button[onclick="changePage(\'prev\')"]');
            const nextButton = document.querySelector('button[onclick="changePage(\'next\')"]');
            const totalPages = getTotalPages();
            
            if (currentPage === 1) {
                prevButton.classList.add('opacity-50', 'cursor-not-allowed');
                prevButton.disabled = true;
            } else {
                prevButton.classList.remove('opacity-50', 'cursor-not-allowed');
                prevButton.disabled = false;
            }
            
            if (currentPage === totalPages) {
                nextButton.classList.add('opacity-50', 'cursor-not-allowed');
                nextButton.disabled = true;
            } else {
                nextButton.classList.remove('opacity-50', 'cursor-not-allowed');
                nextButton.disabled = false;
            }
        }

        // 初始化显示
        document.addEventListener('DOMContentLoaded', function() {
            updateItemsPerPage();
            document.querySelectorAll('.resource-item').forEach((item, index) => {
                if (index < itemsPerPage) {
                    item.classList.add('active');
                }
            });
            updatePaginationButtons();

            // 资源过滤功能
            const filterButtons = document.querySelectorAll('.resource-filter');
            filterButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有按钮的active类
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-blue-800', 'text-white');
                        btn.classList.add('bg-blue-100', 'text-blue-800');
                    });
                    
                    // 添加active类到当前按钮
                    this.classList.remove('bg-blue-100', 'text-blue-800');
                    this.classList.add('active', 'bg-blue-800', 'text-white');
                    
                    const filterType = this.getAttribute('data-filter');
                    const items = document.querySelectorAll('.resource-item');
                    
                    // 重置到第一页
                    currentPage = 1;
                    
                    // 根据类型过滤资源
                    items.forEach(item => {
                        if (filterType === 'all' || item.getAttribute('data-type') === filterType) {
                            item.style.removeProperty('display');
                            item.style.removeProperty('visibility');
                        } else {
                            item.style.display = 'none';
                        }
                    });
                    
                    // 显示第一页
                    const visibleItems = getVisibleItems();
                    visibleItems.forEach((item, index) => {
                        if (index < itemsPerPage) {
                            item.classList.add('active');
                        } else {
                            item.classList.remove('active');
                        }
                    });
                    
                    // 更新分页按钮状态
                    updatePaginationButtons();
                });
            });
        });
    </script>
</body>
</html> 