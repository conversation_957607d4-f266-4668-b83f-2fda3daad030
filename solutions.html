<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案 | 制造业数字化转型专家</title>
    <meta name="description" content="探索我们为制造业提供的智能工厂解决方案，包括MES、WMS、OTD等系统，帮助企业提升效率、降低成本。">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
    </style>
        <script>
        <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:5356023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 页面标题 -->
    <section class="bg-blue-800 pt-24 pb-12 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-4">解决方案</h1>
            <p class="text-xl max-w-3xl">针对制造业不同场景的定制化数字化转型解决方案，提高生产效率、降低运营成本、优化资源配置。</p>
        </div>
    </section>

    <!-- 解决方案导航 -->
    <section class="bg-white py-6 shadow-md sticky top-16 z-5">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap -mx-2">
                <a href="#mes" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">制造执行系统(MES)</a>
                <a href="#wms" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">仓储管理系统(WMS)</a>
                <!-- <a href="#qms" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">质量管理系统(QMS)</a> -->
                <a href="#otd" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">供产销一体化系统(OTD)</a>
                <a href="#dms" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">设备管理系统(DMS)</a>
                <!-- <a href="#apm" class="px-4 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition duration-300">高级生产计划(APM)</a> -->
               
            </div>
        </div>
    </section>

    <!-- MES解决方案 -->
    <section id="mes" class="py-16 pt-20">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row items-center mb-12">
                <div class="lg:w-1/2 lg:pr-12 mb-8 lg:mb-0">
                    <span class="text-blue-600 font-semibold">智能生产管理</span>
                    <h2 class="text-3xl font-bold mb-6">制造执行系统(MES)</h2>
                    <p class="text-gray-600 mb-6">MES系统是连接企业管理层与车间控制层的桥梁，实时监控和管理生产过程，提高生产透明度和效率。</p>
                    <div class="bg-gray-100 p-6 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-4">主要挑战</h3>
                        <ul class="space-y-2">
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>生产数据孤岛，各环节信息不互通，缺乏实时性</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>人工记录生产数据，效率低且容易出错</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>缺乏对生产过程的实时监控，难以快速响应异常</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>资源浪费严重，设备利用率和生产效率低</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <img src="images/solutions/details/mes.jpg" alt="MES制造执行系统" class="rounded-lg shadow-xl w-full solution-image">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">生产计划与排程</h3>
                    <p class="text-gray-600">智能排产算法自动生成最优生产计划，实时调整响应紧急订单，提高计划执行率30%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">质量管控与追溯</h3>
                    <p class="text-gray-600">全过程质量数据采集与分析，实现产品全生命周期追溯，质量问题根源分析时间缩短80%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">能源与效率监控</h3>
                    <p class="text-gray-600">实时监控设备运行状态和能耗数据，智能预警和维护建议，平均设备效率提升18%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">数据可视化分析</h3>
                    <p class="text-gray-600">多维度生产数据可视化，关键绩效指标实时展示，辅助管理决策，管理响应速度提升40%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">工作指导与培训</h3>
                    <p class="text-gray-600">电子化工艺文件和操作指导，AR/VR辅助装配和维修指导，新员工培训周期缩短50%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">异常管理与预警</h3>
                    <p class="text-gray-600">生产异常自动识别与预警，辅助快速处理流程，异常处理时间平均缩短65%。</p>
                </div>
            </div>

            <div class="bg-blue-50 p-8 rounded-lg">
                <h3 class="text-2xl font-bold mb-6 text-center">客户收益</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">25%</div>
                        <p class="text-gray-700">生产效率提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">15%</div>
                        <p class="text-gray-700">生产成本降低</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">30%</div>
                        <p class="text-gray-700">生产计划执行率提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">40%</div>
                        <p class="text-gray-700">质量问题响应速度提升</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="contact.html#demo" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">预约MES系统演示</a>
            </div>
        </div>
    </section>

    <!-- WMS解决方案 -->
    <section id="wms" class="py-16 pt-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row-reverse items-center mb-12">
                <div class="lg:w-1/2 lg:pl-12 mb-8 lg:mb-0">
                    <span class="text-blue-600 font-semibold">智能仓储物流</span>
                    <h2 class="text-3xl font-bold mb-6">仓储管理系统(WMS)</h2>
                    <p class="text-gray-600 mb-6">WMS系统为企业提供全面的仓储管理解决方案，优化库存结构，提高仓储作业效率和准确性，降低运营成本。</p>
                    <div class="bg-gray-100 p-6 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-4">主要挑战</h3>
                        <ul class="space-y-2">
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>库存准确率低，盘点工作量大</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>仓库空间利用率低，存取效率差</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>库存积压与缺货并存，资金占用大</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>人工作业效率低，差错率高</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <img src="images/solutions/details/wms.jpg" alt="WMS仓储管理系统" class="rounded-lg shadow-xl w-full solution-image">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">入库管理</h3>
                    <p class="text-gray-600">自动化收货与验收流程，支持条码/RFID快速扫描，入库效率提升70%，差错率降低95%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">库存管理</h3>
                    <p class="text-gray-600">实时库存监控与预警，智能批次管理与效期控制，库存准确率提升至99.9%，减少呆滞物料30%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">出库管理</h3>
                    <p class="text-gray-600">智能波次分配与路径优化，多订单合并拣选，出库效率提升65%，拣货准确率达99.8%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">库位优化</h3>
                    <p class="text-gray-600">基于货物周转率的智能库位分配，动态调整存储策略，仓库空间利用率提升25%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">数据分析</h3>
                    <p class="text-gray-600">多维度库存分析报表，ABC分类管理，辅助优化采购与库存策略，库存周转率提升35%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">绩效管理</h3>
                    <p class="text-gray-600">员工作业绩效实时监控与评估，精准考核机制，人均工作效率提升40%。</p>
                </div>
            </div>

            <div class="bg-blue-50 p-8 rounded-lg">
                <h3 class="text-2xl font-bold mb-6 text-center">客户收益</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">99.5%</div>
                        <p class="text-gray-700">库存准确率</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">25%</div>
                        <p class="text-gray-700">库存成本降低</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">60%</div>
                        <p class="text-gray-700">作业效率提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">35%</div>
                        <p class="text-gray-700">库存周转率提升</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="contact.html#demo" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">预约WMS系统演示</a>
            </div>
        </div>
    </section>

    <!-- OTD解决方案 -->
    <section id="otd" class="py-16 pt-20">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row items-center mb-12">
                <div class="lg:w-1/2 lg:pr-12 mb-8 lg:mb-0">
                    <span class="text-blue-600 font-semibold">供应链一体化</span>
                    <h2 class="text-3xl font-bold mb-6">供产销一体化系统(OTD)</h2>
                    <p class="text-gray-600 mb-6">OTD (Order to Delivery) 系统提供从订单到交付的全流程管理，打通供应链、生产与销售环节，实现供产销一体化运营，提高企业整体运作效率。</p>
                    <div class="bg-gray-100 p-6 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-4">主要挑战</h3>
                        <ul class="space-y-2">
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>供应链、生产与销售信息孤岛，流程衔接断裂</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>订单响应速度慢，无法快速确认交付承诺</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>库存积压与生产计划脱节，资金占用高</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>物料需求规划与采购管理割裂，无法协同优化</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <img src="images/solutions/details/otd.jpg" alt="供产销一体化系统" class="rounded-lg shadow-xl w-full solution-image">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">订单全流程管理</h3>
                    <p class="text-gray-600">实时订单状态跟踪，智能库存检查与占用，订单响应速度提升60%，交期准确率提高40%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">智能MRP运算</h3>
                    <p class="text-gray-600">基于订单自动触发MRP分析，生成缺料清单，启动采购需求，物料齐套率提升45%，采购提前期缩短30%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">销售预测与库存控制</h3>
                    <p class="text-gray-600">AI驱动的销售预测模型，动态库存优化策略，库存周转率提升35%，库存总量降低28%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">生产计划与排程</h3>
                    <p class="text-gray-600">订单驱动的智能生产排程，多约束条件优化，计划执行率提升30%，生产周期缩短20%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">客户服务集成</h3>
                    <p class="text-gray-600">订单状态实时通知，交付跟踪，异常处理与预警，客户满意度提升45%，投诉处理时间缩短65%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">集成型数据分析</h3>
                    <p class="text-gray-600">多维度业务数据集成分析，订单-生产-交付全链路可视化，决策支持能力提升40%，异常识别提前70%。</p>
                </div>
            </div>

            <div class="bg-blue-50 p-8 rounded-lg">
                <h3 class="text-2xl font-bold mb-6 text-center">客户收益</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">60%</div>
                        <p class="text-gray-700">订单响应速度提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">35%</div>
                        <p class="text-gray-700">库存周转率提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">40%</div>
                        <p class="text-gray-700">交付准时率提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">45%</div>
                        <p class="text-gray-700">客户满意度提升</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="contact.html#demo" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">预约OTD系统演示</a>
            </div>
        </div>
    </section>

    <!-- DMS解决方案 -->
    <section id="dms" class="py-16 pt-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row-reverse items-center mb-12">
                <div class="lg:w-1/2 lg:pl-12 mb-8 lg:mb-0">
                    <span class="text-blue-600 font-semibold">智能设备管理</span>
                    <h2 class="text-3xl font-bold mb-6">设备管理系统(DMS)</h2>
                    <p class="text-gray-600 mb-6">DMS系统提供设备全生命周期管理，实现智能预测性维护，提高设备可用性和生产效率，延长设备使用寿命。</p>
                    <div class="bg-gray-100 p-6 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-4">主要挑战</h3>
                        <ul class="space-y-2">
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>设备临时故障频发，计划外停机时间长</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>设备维护被动响应，缺乏预测性维护</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>设备维护成本高，备件管理混乱</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 mr-2 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>设备整体效率(OEE)低，难以评估</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <img src="images/solutions/details/dms.jpg" alt="DMS设备管理系统" class="rounded-lg shadow-xl w-full solution-image">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">设备台账管理</h3>
                    <p class="text-gray-600">电子化设备档案，设备生命周期管理，技术资料数字化，设备信息查询效率提升90%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">预测性维护</h3>
                    <p class="text-gray-600">设备状态实时监控，基于AI的故障预测分析，预防性维护计划自动生成，设备故障率降低60%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">备件管理</h3>
                    <p class="text-gray-600">备件库存智能优化，需求预测与自动补货，备件库存成本降低25%，备件可用率提升至99.5%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">维护计划管理</h3>
                    <p class="text-gray-600">智能排程的设备维护计划，工单自动分派，移动端执行与验收，计划执行率提升40%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">OEE分析</h3>
                    <p class="text-gray-600">设备综合效率实时监控与分析，瓶颈设备识别，设备OEE平均提升22%。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">故障处理管理</h3>
                    <p class="text-gray-600">故障快速响应机制，知识库辅助诊断，AR远程支持，平均修复时间(MTTR)降低40%。</p>
                </div>
            </div>

            <div class="bg-blue-50 p-8 rounded-lg">
                <h3 class="text-2xl font-bold mb-6 text-center">客户收益</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">60%</div>
                        <p class="text-gray-700">计划外停机时间减少</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">25%</div>
                        <p class="text-gray-700">设备维护成本降低</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">22%</div>
                        <p class="text-gray-700">设备OEE提升</p>
                    </div>
                    <div class="text-center p-4">
                        <div class="text-3xl font-bold text-blue-800 mb-2">15%</div>
                        <p class="text-gray-700">设备使用寿命延长</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="contact.html#demo" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">预约DMS系统演示</a>
            </div>
        </div>
    </section>

    <!-- 咨询入口 -->
    <!-- <section class="py-16 bg-gray-800 text-white text-center">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold mb-6">开启您的数字化转型之旅</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">我们的专业团队将为您提供定制化解决方案，助力您的企业实现降本增效、提质增产的目标。</p>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="contact.html" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md font-medium transition duration-300">联系我们</a>
                <a href="contact.html#demo" class="bg-white hover:bg-gray-100 text-blue-800 px-8 py-3 rounded-md font-medium transition duration-300">预约演示</a>
            </div>
        </div>
    </section> -->
    <!-- 咨询入口 -->
    <section class="py-16 bg-blue-800 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">准备开启您的数字化转型之旅？</h2>
            <p class="max-w-3xl mx-auto mb-8 text-lg">我们的专业团队将为您提供一对一咨询，详细了解您的需求，并提供定制化解决方案。</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="contact.html#demo" class="bg-white text-blue-800 px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition duration-300">预约演示</a>
                <a href="tel:+86051268666026" class="border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-blue-800 transition duration-300">立即咨询: 0512-68666026</a>
            </div>
        </div>
    </section>
    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="js/components.js?v=1.0.1"></script>
    
</body>
</html> 