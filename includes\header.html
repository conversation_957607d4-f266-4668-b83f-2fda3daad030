<!-- 页眉导航 -->
<header class="bg-white shadow-md fixed w-full top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            <a href="index.html" class="flex items-center">
                <img src="images/logo.png" alt="智慧工厂" class="h-10">
                <span class="ml-3 text-xl font-bold text-blue-800">智慧工厂</span>
            </a>
            <nav class="hidden md:flex space-x-10">
                <a href="index.html" class="text-gray-700 hover:text-blue-800 transition">首页</a>
                <div class="relative group">
                    <button class="text-gray-700 group-hover:text-blue-800 flex items-center transition">
                        解决方案
                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="absolute left-0 mt-2 w-56 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-200 transform origin-top-left">
                        <div class="bg-white border rounded-lg shadow-lg py-2">
                            <a href="solutions.html#mes" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">制造执行系统(MES)</a>
                            <a href="solutions.html#wms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">仓储管理系统(WMS)</a>
                            <a href="solutions.html#otd" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">供产销一体化系统(OTD)</a>
                            <!-- <a href="solutions.html#qms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">质量管理系统(QMS)</a> -->
                            <a href="solutions.html#dms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">设备管理系统(DMS)</a>
                           
                        </div>
                    </div>
                </div>
                <a href="cases.html" class="text-gray-700 hover:text-blue-800 transition">客户案例</a>
                <a href="resources.html" class="text-gray-700 hover:text-blue-800 transition">资源中心</a>
                <a href="about.html" class="text-gray-700 hover:text-blue-800 transition">关于我们</a>
                <a href="contact.html" class="text-gray-700 hover:text-blue-800 transition">联系我们</a>
            </nav>
            <div class="hidden md:block">
                <a href="contact.html#demo" class="inline-flex items-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-800 hover:bg-blue-700">
                    预约演示
                </a>
            </div>
            <button class="md:hidden flex items-center text-gray-700">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </div>
</header>

<!-- 移动菜单 -->
<div id="mobile-menu" class="hidden fixed inset-0 z-50 md:hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="absolute right-0 top-0 bottom-0 w-64 bg-white p-6">
        <div class="flex justify-end">
            <button id="close-menu" class="text-gray-700">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <nav class="mt-6">
            <a href="index.html" class="block py-2 text-gray-700 hover:text-blue-800">首页</a>
            <div class="mb-4">
                <button class="flex items-center justify-between w-full text-gray-700 hover:text-blue-800">
                    解决方案
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="mt-2 pl-4 hidden">
                    <a href="solutions.html#mes" class="block py-2 text-gray-700 hover:text-blue-800">制造执行系统(MES)</a>
                    <a href="solutions.html#wms" class="block py-2 text-gray-700 hover:text-blue-800">仓储管理系统(WMS)</a>
                    <a href="solutions.html#qms" class="block py-2 text-gray-700 hover:text-blue-800">质量管理系统(QMS)</a>
                    <a href="solutions.html#otd" class="block py-2 text-gray-700 hover:text-blue-800">供产销一体化系统(OTD)</a>
                    <a href="solutions.html#dms" class="block py-2 text-gray-700 hover:text-blue-800">设备管理系统(DMS)</a>                    
                </div>
            </div>
            <a href="cases.html" class="block py-2 text-gray-700 hover:text-blue-800">客户案例</a>
            <a href="resources.html" class="block py-2 text-gray-700 hover:text-blue-800">资源中心</a>
            <a href="about.html" class="block py-2 text-gray-700 hover:text-blue-800">关于我们</a>
            <a href="contact.html" class="block py-2 text-gray-700 hover:text-blue-800">联系我们</a>
            <div class="mt-6">
                <a href="contact.html#demo" class="inline-flex items-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-800 hover:bg-blue-700">
                    预约演示
                </a>
            </div>
        </nav>
    </div>
</div>

<!-- 页面顶部空间 (为固定导航腾出空间) -->
<div class="h-20"></div> 