document.addEventListener('DOMContentLoaded', function() {
    // 加载组件前先应用一些样式以防止闪烁
    applyPreLoadStyles();
    
    // 替换HTML中的Alpine.js CDN引用为本地文件
    replaceAlpineJsCdnReferences();
    
    // 加载组件
    loadComponents();
    
    // 初始化各种功能 - 不使用setTimeout
    initMobileMenu();
    initSolutionsDropdown();
    initSmoothScrolling();
});

/**
 * 应用预加载样式以防止闪烁
 */
function applyPreLoadStyles() {
    // 添加一个样式元素到头部
    const style = document.createElement('style');
    style.innerHTML = `
        .page-transition {
            opacity: 1;
            transition: opacity 0.1s ease-in-out;
        }
        body.page-loading {
            opacity: 0;
        }
    `;
    document.head.appendChild(style);
    
    // 给body添加过渡类
    document.body.classList.add('page-transition');
    document.body.classList.remove('page-loading');
}

/**
 * 替换HTML页面中的Alpine.js CDN引用为本地文件
 */
function replaceAlpineJsCdnReferences() {
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src && (src.includes('alpinejs') || src.includes('alpine') || src.includes('cdn.min.js'))) {
            // 替换为本地文件
            script.setAttribute('src', 'js/vendor/alpine.min.js');
            console.log('已将Alpine.js CDN引用替换为本地文件');
        }
    });
}

/**
 * 添加页面过渡效果
 */
function addPageTransitions() {
    // 移除页面加载样式阻止闪烁
    document.body.classList.remove('page-loading');

    // 为页面链接添加过渡效果
    const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript:"]):not([href^="mailto:"]):not([href^="tel:"])');
    
    links.forEach(link => {
        // 只处理域内链接
        if (link.hostname === window.location.hostname || link.hostname === '') {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                
                // 如果是同站点链接，就添加过渡效果
                // if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                //     // 特殊处理：如果当前页面是solutions.html并且链接是solutions.html#xxx形式
                //     const currentPath = window.location.pathname;
                //     const isCurrentSolutions = currentPath.endsWith('solutions.html');
                //     const isSolutionsAnchor = href.includes('solutions.html#');
                    
                //     // 特殊处理：行业解决方案页面的链接
                //     const isIndustryAnchor = href.includes('industry.html#');
                    
                //     if ((isCurrentSolutions && isSolutionsAnchor) || isIndustryAnchor) {
                //         // 不阻止默认行为，让浏览器正常处理锚点跳转
                //         return;
                //     }
                    
                //     e.preventDefault();
                //     document.body.style.opacity = 0;
                    
                //     // 立即跳转到目标页面，不需要等待
                //     setTimeout(() => {
                //         window.location.href = href;
                //     }, 100); // 只等待很短的时间让过渡效果有机会启动
                // }
            });
        }
    });
}

/**
 * 加载组件
 */
function loadComponents() {
    // 加载头部
    const headerContainer = document.querySelector('#header-container');
    if (headerContainer) {
        // 获取页面完整URL路径并进行分析
        const currentPath = window.location.pathname;
        let pathPrefix = '';
        
        // 判断是否在子目录中
        if (currentPath.includes('/cases/') || 
            currentPath.includes('/solutions/') || 
            currentPath.includes('/resources/')) {
            // 如果在子目录中，使用相对路径返回上一级
            pathPrefix = '../';
        // } else if (currentPath.includes('/manufacturing-website/')) {
        //     // 如果当前已经在manufacturing-website目录下的根页面
        //     pathPrefix = '';
        // } else {
        //     // 如果不在manufacturing-website目录下，添加完整路径
        //     pathPrefix = '/manufacturing-website/';
        }
        
        headerContainer.innerHTML = `
<!-- 页头导航 -->
<header class="bg-white shadow-md fixed w-full top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            <a href="${pathPrefix}index.html" class="flex items-center">
                <img src="${pathPrefix}images/logo.png" alt="奥斯坦丁" class="h-10 mr-3">
                <span class="text-xl font-bold text-blue-800">奥斯坦丁</span>
            </a>
            <nav class="hidden md:flex space-x-10">
                <a href="${pathPrefix}index.html" class="text-gray-700 hover:text-blue-800 transition">首页</a>
                <div class="relative solutions-dropdown">
                    <button class="solutions-button text-gray-700 hover:text-blue-800 flex items-center transition">
                        解决方案
                        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solutions-menu absolute left-0 mt-2 w-56 hidden bg-white border rounded-lg shadow-lg py-2">
                        <a href="${pathPrefix}solutions.html#mes" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">制造执行系统(MES)</a>
                        <a href="${pathPrefix}solutions.html#wms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">仓储管理系统(WMS)</a>
                        <a href="${pathPrefix}solutions.html#otd" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">供产销一体化系统(OTD)</a>
                        <!-- <a href="${pathPrefix}solutions.html#qms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">质量管理系统(QMS)</a> -->
                        <a href="${pathPrefix}solutions.html#dms" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-800">设备管理系统(DMS)</a>
                    </div>
                </div>
                <a href="${pathPrefix}solutions/industry.html" class="text-gray-700 hover:text-blue-800 transition">行业解决方案</a>
                <a href="${pathPrefix}cases.html" class="text-gray-700 hover:text-blue-800 transition">客户案例</a>
                <a href="${pathPrefix}resources.html" class="text-gray-700 hover:text-blue-800 transition">资源中心</a>
                <a href="${pathPrefix}about.html" class="text-gray-700 hover:text-blue-800 transition">关于我们</a>
                <a href="${pathPrefix}contact.html" class="text-gray-700 hover:text-blue-800 transition">联系我们</a>
                <a href="https://blog.otdmes.com" class="text-gray-700 hover:text-blue-800 transition">行业资讯</a>
                <a href="http://langdianrobot.com"  target="_Blank" class="text-gray-700 hover:text-blue-800 transition">朗电机器人</a>
            </nav>
            <div class="hidden md:block">
                <a href="${pathPrefix}contact.html#demo" class="inline-flex items-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-800 hover:bg-blue-700">
                    预约演示
                </a>
            </div>
            <button class="md:hidden flex items-center text-gray-700">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </div>
</header>

<!-- 移动菜单 -->
<div id="mobile-menu" class="hidden fixed inset-0 z-50 md:hidden">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="absolute right-0 top-0 bottom-0 w-64 bg-white p-6">
        <div class="flex justify-end">
            <button id="close-menu" class="text-gray-700">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <nav class="mt-6">
            <div class="flex items-center mb-6">
                <img src="${pathPrefix}images/logo.png" alt="奥斯坦丁" class="h-8 mr-2">
                <span class="text-lg font-bold text-blue-800">奥斯坦丁</span>
            </div>
            <a href="${pathPrefix}index.html" class="block py-2 text-gray-700 hover:text-blue-800">首页</a>
            <div class="mb-4">
                <button class="mobile-solutions-button flex items-center justify-between w-full text-gray-700 hover:text-blue-800">
                    解决方案
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="mobile-solutions-menu mt-2 pl-4 hidden">
                    <a href="${pathPrefix}solutions.html#mes" class="block py-2 text-gray-700 hover:text-blue-800">制造执行系统(MES)</a>
                    <a href="${pathPrefix}solutions.html#wms" class="block py-2 text-gray-700 hover:text-blue-800">仓储管理系统(WMS)</a>                       
                    <a href="${pathPrefix}solutions.html#otd" class="block py-2 text-gray-700 hover:text-blue-800">供产销一体化系统(OTD)</a>
                    <a href="${pathPrefix}solutions.html#dms" class="block py-2 text-gray-700 hover:text-blue-800">设备管理系统(DMS)</a>                   
                </div>
            </div>
            <a href="${pathPrefix}solutions/industry.html" class="block py-2 text-gray-700 hover:text-blue-800">行业解决方案</a>
            <a href="${pathPrefix}cases.html" class="block py-2 text-gray-700 hover:text-blue-800">客户案例</a>
            <a href="${pathPrefix}resources.html" class="block py-2 text-gray-700 hover:text-blue-800">资源中心</a>
            <a href="${pathPrefix}about.html" class="block py-2 text-gray-700 hover:text-blue-800">关于我们</a>
            <a href="${pathPrefix}contact.html" class="block py-2 text-gray-700 hover:text-blue-800">联系我们</a>
            <a href="https://blog.otdmes.com" class="block py-2 text-gray-700 hover:text-blue-800">行业资讯</a>
            <a href="http://langdianrobot.com"  target="_Blank"  class="block py-2 text-gray-700 hover:text-blue-800">朗电机器人</a>
            <div class="mt-6">
                <a href="${pathPrefix}contact.html#demo" class="inline-flex items-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-800 hover:bg-blue-700">
                    预约演示
                </a>
            </div>
        </nav>
    </div>
</div>

<!-- 页面顶部空间 (为固定导航腾出空间) -->
<div class="h-16"></div>
        `;
    }
    
    // 加载页脚
    const footerContainer = document.querySelector('#footer-container');
    if (footerContainer) {
        // 获取页面完整URL路径并进行分析
        const currentPath = window.location.pathname;
        let pathPrefix = '';
        
        // 判断是否在子目录中
        if (currentPath.includes('/cases/') || 
            currentPath.includes('/solutions/') || 
            currentPath.includes('/resources/')) {
            // 如果在子目录中，使用相对路径返回上一级
            pathPrefix = '../';
        // } else if (currentPath.includes('/manufacturing-website/')) {
        //     // 如果当前已经在manufacturing-website目录下的根页面
        //     pathPrefix = '';
        // } else {
        //     // 如果不在manufacturing-website目录下，添加完整路径
        //     pathPrefix = '/manufacturing-website/';
        }
        
        footerContainer.innerHTML = `
<!-- 页脚 -->
<footer class="bg-gray-800 text-white pt-16 pb-8">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div>
                <h4 class="text-xl font-bold mb-6">公司简介</h4>
                <p class="text-gray-300 mb-6">我们是专注于制造业数字化转型的解决方案提供商，拥有8年行业经验和众多500强企业成功案例。</p>

            </div>
            <div>
                <h4 class="text-xl font-bold mb-6">解决方案</h4>
                <ul class="space-y-3">
                    <li><a href="${pathPrefix}solutions.html#mes" class="text-gray-300 hover:text-white">制造执行系统(MES)</a></li>
                    <li><a href="${pathPrefix}solutions.html#wms" class="text-gray-300 hover:text-white">仓储管理系统(WMS)</a></li>
                    <li><a href="${pathPrefix}solutions.html#otd" class="text-gray-300 hover:text-white">供产销一体化系统(OTD)</a></li>
                    <li><a href="${pathPrefix}solutions.html#dms" class="text-gray-300 hover:text-white">设备管理系统(DMS)</a></li>
                    
                </ul>
            </div>
            <div>
                <h4 class="text-xl font-bold mb-6">行业解决方案</h4>
                <ul class="space-y-3">
                    <li><a href="${pathPrefix}solutions/industry.html" class="text-gray-300 hover:text-white">汽车制造</a></li>
                    <li><a href="${pathPrefix}solutions/industry.html#electronics" class="text-gray-300 hover:text-white">电子制造</a></li>          
                    <li><a href="${pathPrefix}solutions/industry.html#food" class="text-gray-300 hover:text-white">食品饮料</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-xl font-bold mb-6">联系我们</h4>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="h-6 w-6 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-gray-300">江苏省苏州市工业园区林泉街399号东南大学成贤院304、306</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="h-6 w-6 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span class="text-gray-300">0512-68666026</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="h-6 w-6 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-gray-300"><EMAIL></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="pt-8 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center">
            <div class="mb-4 md:mb-0">
                <p class="text-gray-400">© 2024 苏州奥斯坦丁软件科技有限公司. 保留所有权利</p>
            </div>
        </div>
    </div>
</footer>

<!-- 聊天机器人 -->
<div class="fixed right-6 bottom-6 z-10">
    <button id="chat-button" class="bg-blue-800 text-white w-16 h-16 rounded-full shadow-lg flex items-center justify-center hover:bg-blue-700 transition duration-300">
        <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    </button>
</div>
        `;
    }

    // 初始化页面转换效果
    setTimeout(addPageTransitions, 200);
}

/**
 * 初始化解决方案下拉菜单
 */
function initSolutionsDropdown() {
    // 桌面版下拉菜单
    const dropdownButton = document.querySelector('.solutions-button');
    const dropdownMenu = document.querySelector('.solutions-menu');
    const dropdownContainer = document.querySelector('.solutions-dropdown');
    
    if (dropdownButton && dropdownMenu && dropdownContainer) {
        let timeoutId = null;
        
        // 设置下拉菜单的z-index确保高于其他元素
        dropdownMenu.style.zIndex = "30";
        
        // 点击按钮切换菜单显示状态
        dropdownButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropdownMenu.classList.toggle('hidden');
        });
        
        // 鼠标进入菜单容器时显示菜单
        dropdownContainer.addEventListener('mouseenter', function() {
            // 清除任何先前的定时器
            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }
            dropdownMenu.classList.remove('hidden');
        });
        
        // 鼠标进入菜单时保持显示
        dropdownMenu.addEventListener('mouseenter', function() {
            // 清除任何先前的定时器
            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }
        });
        
        // 鼠标离开菜单容器时延迟隐藏菜单
        dropdownContainer.addEventListener('mouseleave', function(e) {
            // 如果鼠标移向的是下拉菜单，不做任何处理
            if (e.relatedTarget && dropdownMenu.contains(e.relatedTarget)) {
                return;
            }
            
            // 设置延迟隐藏，给用户更多时间移动到菜单上
            timeoutId = setTimeout(function() {
                dropdownMenu.classList.add('hidden');
            }, 300); // 300毫秒延迟
        });
        
        // 鼠标离开菜单时延迟隐藏
        dropdownMenu.addEventListener('mouseleave', function(e) {
            // 如果鼠标移向的是菜单容器，不做任何处理
            if (e.relatedTarget && dropdownContainer.contains(e.relatedTarget)) {
                return;
            }
            
            // 设置延迟隐藏
            timeoutId = setTimeout(function() {
                dropdownMenu.classList.add('hidden');
            }, 300); // 300毫秒延迟
        });
        
        // 点击菜单项时隐藏菜单
        dropdownMenu.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function() {
                dropdownMenu.classList.add('hidden');
            });
        });
        
        // 点击页面其他区域时隐藏菜单
        document.addEventListener('click', function(e) {
            if (!dropdownContainer.contains(e.target)) {
                dropdownMenu.classList.add('hidden');
            }
        });
    }
    
    // 移动版下拉菜单
    const mobileSolutionsButton = document.querySelector('.mobile-solutions-button');
    const mobileSolutionsMenu = document.querySelector('.mobile-solutions-menu');
    
    if (mobileSolutionsButton && mobileSolutionsMenu) {
        mobileSolutionsButton.addEventListener('click', function() {
            mobileSolutionsMenu.classList.toggle('hidden');
        });
    }
}

/**
 * 初始化移动菜单
 */
function initMobileMenu() {
    const menuButton = document.querySelector('header button.md\\:hidden');
    const mobileMenu = document.querySelector('#mobile-menu');
    const closeButton = document.querySelector('#close-menu');
    
    if (menuButton && mobileMenu && closeButton) {
        menuButton.addEventListener('click', function() {
            mobileMenu.classList.remove('hidden');
        });
        
        closeButton.addEventListener('click', function() {
            mobileMenu.classList.add('hidden');
        });
    }
    
    // 高亮当前页面的导航链接
    highlightCurrentPage();
}

/**
 * 高亮当前页面的导航链接
 */
function highlightCurrentPage() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        const linkPath = link.getAttribute('href');
        if (currentPath.includes(linkPath) && linkPath !== '/') {
            link.classList.add('text-blue-800');
            link.classList.remove('text-gray-700');
        }
    });
}

/**
 * 初始化锚点平滑跳转
 */
function initSmoothScrolling() {
    // 获取所有解决方案导航链接，包括纯锚点和带页面名的锚点
    const solutionNavLinks = document.querySelectorAll('a[href^="#"], a[href*="solutions.html#"], a[href*="industry.html#"]');
    
    // 为每个导航链接添加点击事件监听器
    solutionNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 获取完整href
            const href = this.getAttribute('href');
            
            // 处理不同类型的链接
            let targetId;
            
            // 如果是纯锚点链接
            if (href.startsWith('#')) {
                targetId = href.substring(1);
            } 
            // 如果是带页面名的锚点链接（solutions.html#xxx或industry.html#xxx格式）
            else if (href.includes('#')) {
                // 检查当前是否已在solutions页面或industry页面
                const currentPath = window.location.pathname;
                const isCurrentSolutions = currentPath.endsWith('solutions.html');
                const isCurrentIndustry = currentPath.endsWith('industry.html');
                const isSolutionsAnchor = href.includes('solutions.html#');
                const isIndustryAnchor = href.includes('industry.html#');
                
                if ((isCurrentSolutions && isSolutionsAnchor) || 
                    (isCurrentIndustry && isIndustryAnchor)) {
                    // 如果已经在相应页面，只获取#之后的部分
                    targetId = href.split('#')[1];
                } else {
                    // 如果是从其他页面跳转到industry.html，需要特殊处理
                    if (isIndustryAnchor) {
                        // 不阻止默认行为，让浏览器正常跳转
                        // 页面加载完成后会自动处理切换标签
                        return;
                    }
                    // 如果是其他页面，让浏览器默认处理
                    return;
                }
            } else {
                // 不是锚点链接，让浏览器默认处理
                return;
            }
            
            const targetElement = document.getElementById(targetId);
            
            // 如果找到了目标元素
            if (targetElement) {
                e.preventDefault();
                
                // 平滑滚动到目标位置，考虑导航栏高度
                window.scrollTo({
                    top: targetElement.offsetTop - 90, // 减去导航栏高度和一些额外空间
                    behavior: 'smooth'
                });
                
                // 更新URL
                history.pushState(null, null, `#${targetId}`);
                
                // 如果是行业解决方案页面，还需要手动触发切换标签
                const currentPath = window.location.pathname;
                const isCurrentIndustry = currentPath.endsWith('industry.html');
                if (isCurrentIndustry && ['auto', 'electronics', 'food'].includes(targetId)) {
                    // 调用industry.html页面中的switchTab函数
                    if (typeof switchTab === 'function') {
                        switchTab(targetId);
                    }
                }
            }
        });
    });
    
    // 检查URL中是否有锚点，如果有则滚动到相应位置
    if (window.location.hash) {
        const targetId = window.location.hash.substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            // 延迟执行以确保页面已完全加载
            setTimeout(() => {
                window.scrollTo({
                    top: targetElement.offsetTop - 90,
                    behavior: 'smooth'
                });
            }, 300);
        }
    }
}