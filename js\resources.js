document.addEventListener('DOMContentLoaded', function() {
    // 等待组件加载完成后初始化资源筛选功能
    setTimeout(initResourceFilters, 200);
    
    // 添加聊天按钮功能
    initChatButton();
});

/**
 * 初始化资源筛选功能
 */
function initResourceFilters() {
    const filterButtons = document.querySelectorAll('.resource-filter');
    const resourceItems = document.querySelectorAll('.resource-item');
    
    if (filterButtons.length && resourceItems.length) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有按钮的active类
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-blue-800', 'text-white');
                    btn.classList.add('bg-blue-100', 'text-blue-800');
                });
                
                // 为当前按钮添加active类
                this.classList.add('active', 'bg-blue-800', 'text-white');
                this.classList.remove('bg-blue-100', 'text-blue-800');
                
                const filter = this.getAttribute('data-filter');
                
                // 筛选资源
                resourceItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-type') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    }
}

/**
 * 初始化聊天按钮
 */
function initChatButton() {
    const chatButton = document.getElementById('chat-button');
    if (chatButton) {
        chatButton.addEventListener('click', function() {
            alert('聊天功能即将上线，敬请期待！');
        });
    }
} 