// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
    
    // 聊天机器人功能
    const chatButton = document.getElementById('chat-button');
    
    if (chatButton) {
        chatButton.addEventListener('click', function() {
            // 检查是否已存在聊天气泡
            let chatBubble = document.querySelector('.chat-bubble');
            
            if (chatBubble) {
                // 切换显示/隐藏状态
                const isVisible = chatBubble.style.display === 'block';
                chatBubble.style.display = isVisible ? 'none' : 'block';
            } else {
                // 创建新的聊天气泡
                chatBubble = document.createElement('div');
                chatBubble.className = 'chat-bubble';
                chatBubble.style.display = 'block';
                
                // 添加聊天界面HTML
                chatBubble.innerHTML = `
                    <div class="chat-header">在线客服</div>
                    <div class="chat-messages">
                        <div class="p-3 bg-gray-100 rounded-lg mb-3 mr-12">
                            <p>您好！我是智慧工厂的智能客服，请问有什么可以帮您？</p>
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" placeholder="请输入您的问题..." />
                        <button>发送</button>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(chatBubble);
                
                // 绑定发送消息事件
                const chatInput = chatBubble.querySelector('input');
                const chatSendButton = chatBubble.querySelector('button');
                const chatMessages = chatBubble.querySelector('.chat-messages');
                
                // 发送按钮点击事件
                chatSendButton.addEventListener('click', function() {
                    sendMessage(chatInput, chatMessages);
                });
                
                // 输入框回车事件
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage(chatInput, chatMessages);
                    }
                });
            }
        });
    }
    
    // 发送消息函数
    function sendMessage(inputElement, messagesElement) {
        const message = inputElement.value.trim();
        if (message) {
            // 添加用户消息
            const userMessageDiv = document.createElement('div');
            userMessageDiv.className = 'p-3 bg-blue-100 rounded-lg mb-3 ml-12 text-right';
            userMessageDiv.innerHTML = `<p>${message}</p>`;
            messagesElement.appendChild(userMessageDiv);
            
            // 清空输入框
            inputElement.value = '';
            
            // 滚动到底部
            messagesElement.scrollTop = messagesElement.scrollHeight;
            
            // 模拟回复（实际项目中可替换为API调用）
            setTimeout(function() {
                const responseDiv = document.createElement('div');
                responseDiv.className = 'p-3 bg-gray-100 rounded-lg mb-3 mr-12';
                
                // 简单的自动回复逻辑
                let responseText = '感谢您的咨询，我们的客服人员会尽快与您联系。';
                
                if (message.includes('价格') || message.includes('报价') || message.includes('多少钱')) {
                    responseText = '我们的解决方案价格根据您的具体需求定制，请联系我们的销售团队获取详细报价：************';
                } else if (message.includes('演示') || message.includes('demo')) {
                    responseText = '您可以通过首页的"预约演示"按钮，填写相关信息，我们会安排专人为您演示系统功能。';
                } else if (message.includes('部署') || message.includes('实施') || message.includes('周期')) {
                    responseText = '我们的解决方案一般可在2-4周内完成部署，具体取决于项目规模和复杂度。';
                }
                
                responseDiv.innerHTML = `<p>${responseText}</p>`;
                messagesElement.appendChild(responseDiv);
                
                // 滚动到底部
                messagesElement.scrollTop = messagesElement.scrollHeight;
            }, 1000);
        }
    }
    
    // 数字动画效果
    const animateNumbers = () => {
        const counters = document.querySelectorAll('.count-up');
        const speed = 200;
        
        counters.forEach(counter => {
            const animate = () => {
                const value = +counter.getAttribute('data-target');
                const data = +counter.innerText;
                
                const time = value / speed;
                if (data < value) {
                    counter.innerText = Math.ceil(data + time);
                    setTimeout(animate, 1);
                } else {
                    counter.innerText = value;
                }
            }
            animate();
        });
    };
    
    // 检测元素是否在可视区域
    const isInViewport = (element) => {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    };
    
    // 滚动时检测并触发动画
    window.addEventListener('scroll', () => {
        const statSection = document.querySelector('.count-up');
        if (statSection && isInViewport(statSection)) {
            animateNumbers();
            // 移除监听器，避免重复触发
            window.removeEventListener('scroll', this);
        }
    });
    
    // 表单验证
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            let isValid = true;
            const nameInput = document.getElementById('name');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');
            const messageInput = document.getElementById('message');
            
            // 重置错误提示
            document.querySelectorAll('.error-message').forEach(el => el.remove());
            
            // 姓名验证
            if (!nameInput.value.trim()) {
                showError(nameInput, '请输入您的姓名');
                isValid = false;
            }
            
            // 邮箱验证
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(emailInput.value.trim())) {
                showError(emailInput, '请输入有效的邮箱地址');
                isValid = false;
            }
            
            // 电话验证（可选）
            if (phoneInput.value.trim() && !/^1[3-9]\d{9}$/.test(phoneInput.value.trim())) {
                showError(phoneInput, '请输入有效的手机号码');
                isValid = false;
            }
            
            // 消息验证
            if (!messageInput.value.trim()) {
                showError(messageInput, '请输入您的咨询内容');
                isValid = false;
            }
            
            if (isValid) {
                // 在实际应用中，这里应该是一个AJAX请求发送表单数据
                const submitButton = contactForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                submitButton.textContent = '提交中...';
                
                // 模拟提交后的成功响应
                setTimeout(() => {
                    contactForm.innerHTML = `
                        <div class="bg-green-100 text-green-700 p-4 rounded-lg text-center">
                            <svg class="w-10 h-10 mx-auto mb-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <h3 class="text-xl font-bold mb-2">提交成功！</h3>
                            <p>感谢您的咨询，我们的团队将在24小时内与您联系。</p>
                        </div>
                    `;
                }, 1500);
            }
        });
    }
    
    // 显示错误提示
    function showError(inputElement, message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
        errorDiv.textContent = message;
        inputElement.parentNode.appendChild(errorDiv);
        inputElement.classList.add('border-red-500');
    }

    // 初始化页面效果
    const init = () => {
        // 给所有卡片添加入场动画
        document.querySelectorAll('.case-card, .stat-card').forEach((el, index) => {
            el.classList.add('animate-fade-in');
            el.style.animationDelay = `${index * 0.1}s`;
        });
    };
    
    init();

    // 资源筛选功能
    function initResourceFilters() {
        const resourceFilters = document.querySelectorAll('.resource-filter');
        const resourceItems = document.querySelectorAll('.resource-item');
        
        if (resourceFilters.length > 0) {
            resourceFilters.forEach(filter => {
                filter.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有筛选器的active类
                    resourceFilters.forEach(f => f.classList.remove('active', 'bg-blue-800', 'text-white'));
                    resourceFilters.forEach(f => f.classList.add('bg-blue-100', 'text-blue-800'));
                    
                    // 为当前筛选器添加active类
                    this.classList.add('active', 'bg-blue-800', 'text-white');
                    this.classList.remove('bg-blue-100', 'text-blue-800');
                    
                    const filterType = this.getAttribute('data-filter');
                    
                    // 显示或隐藏资源项
                    resourceItems.forEach(item => {
                        if (filterType === 'all' || item.getAttribute('data-type') === filterType) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }
    }

    // 初始化资源筛选功能
    initResourceFilters();
}); 