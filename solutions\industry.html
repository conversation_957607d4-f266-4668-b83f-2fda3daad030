<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业解决方案 - 制造业数字化转型</title>
    <meta name="description" content="为不同行业提供专业的制造业数字化转型解决方案，包括汽车制造、电子制造、食品饮料等行业">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="../js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        body.loaded {
            opacity: 1;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 页面标题 -->
    <section class="bg-blue-800 pt-24 pb-12 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-4">行业解决方案</h1>
            <p class="text-xl max-w-3xl">为不同行业提供量身定制的数字化转型解决方案，解决行业特定痛点，提升生产效率与管理水平。</p>
        </div>
    </section>

    <!-- 导航按钮 -->
    <section class="bg-white py-6 shadow-md sticky top-16 z-5">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap justify-center -mx-2">
                <button onclick="switchTab('auto')" class="tab-button px-6 py-2 mx-2 mb-2 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                    汽车零部件
                </button>
                <button onclick="switchTab('electronics')" class="tab-button px-6 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition-colors">
                    电子制造
                </button>
                <button onclick="switchTab('food')" class="tab-button px-6 py-2 mx-2 mb-2 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-800 hover:text-white transition-colors">
                    食品饮料
                </button>
            </div>
        </div>
    </section>

    <!-- 内容区域 -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- 汽车制造解决方案 -->
        <div id="auto" class="tab-content active">
            <!-- 行业痛点 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">行业痛点</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">生产管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">生产计划排程不准确，导致库存积压或断供</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">设备利用率低，维护成本高</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">生产过程不透明，难以实时监控</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">物流管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">物料库存管理不准确，影响生产计划</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">物料周转效率低，占用大量资金</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">物料追溯困难，影响质量管理</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 解决方案 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">解决方案</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">MES系统</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">智能生产计划排程</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">设备状态实时监控</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">生产过程可视化</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">WMS系统</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">智能库存管理</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">物料周转优化</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">物料追溯管理</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 应用效果 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">应用效果</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">30%</div>
                        <div class="text-gray-600">生产效率提升</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">99.9%</div>
                        <div class="text-gray-600">库存准确率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">40%</div>
                        <div class="text-gray-600">物料周转提升</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">50%</div>
                        <div class="text-gray-600">计划响应时间缩短</div>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">成功案例</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">无锡夏普智能制造升级</h4>
                        <p class="text-gray-600 mb-4">通过实施MES和WMS系统，实现生产计划智能排程、设备状态实时监控、物料库存精准管理，显著提升生产效率。</p>
                        <a href="../cases/case1.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div>
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">中汇瑞德数字化转型</h4>
                        <p class="text-gray-600 mb-4">通过数字化系统建设，实现生产过程可视化、物料管理智能化、质量追溯全程化，大幅提升运营效率。</p>
                        <a href="../cases/case3.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 电子制造解决方案 -->
        <div id="electronics" class="tab-content">
            <!-- 行业痛点 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">行业痛点</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">生产管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">设备故障难以预测，维护成本高</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">能源消耗大，成本高</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">生产效率低，良品率不稳定</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">质量管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">质量追溯困难，问题定位慢</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">不良品率高，返工成本大</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">质量改进效果不明显</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 解决方案 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">解决方案</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">数字孪生系统</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">设备可视化监控</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">预测性维护</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">虚拟仿真优化</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">物联网平台</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">数据采集分析</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">实时监控预警</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">智能决策支持</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 应用效果 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">应用效果</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">85%</div>
                        <div class="text-gray-600">设备故障预测准确率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">45%</div>
                        <div class="text-gray-600">维护成本降低</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">32%</div>
                        <div class="text-gray-600">生产效率提升</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">28%</div>
                        <div class="text-gray-600">能源消耗降低</div>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">成功案例</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">伟创力智能工厂建设</h4>
                        <p class="text-gray-600 mb-4">通过实施数字孪生系统和物联网平台，实现设备预测性维护、生产过程优化、能源管理智能化，显著提升运营效率。</p>
                        <a href="../cases/case2.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div>
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">北陆微电子数字化转型</h4>
                        <p class="text-gray-600 mb-4">通过数字化系统建设，实现生产过程可视化、设备管理智能化、质量追溯全程化，大幅提升运营效率。</p>
                        <a href="../cases/case4.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 食品饮料解决方案 -->
        <div id="food" class="tab-content">
            <!-- 行业痛点 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">行业痛点</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">生产管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">配方管理复杂，配料称重易错</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">温度控制不准确，影响产品质量</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">生产效率低，人工成本高</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">质量管理</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">质量追溯困难，食品安全风险高</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">保质期管理不准确，库存损失大</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span class="text-gray-600">质量改进效果不明显</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 解决方案 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">解决方案</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">配方管理系统</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">配方智能管理</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">配料自动称重</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">防错防呆控制</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">质量追溯系统</h4>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">全程质量追溯</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">温度实时监控</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="h-6 w-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-600">保质期管理</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 应用效果 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 mb-4">应用效果</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">100%</div>
                        <div class="text-gray-600">产品质量追溯率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">35%</div>
                        <div class="text-gray-600">生产效率提升</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">0%</div>
                        <div class="text-gray-600">配料称重错误率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">95%</div>
                        <div class="text-gray-600">温度控制准确率</div>
                    </div>
                </div>
            </div>

            <!-- 成功案例 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">成功案例</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">日世冰淇淋数字化产线建设</h4>
                        <p class="text-gray-600 mb-4">通过实施配方管理系统和质量追溯系统，实现配料智能称重、温度精准控制、质量全程追溯，显著提升产品质量。</p>
                        <a href="../cases/case10.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div>
                    <!-- <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">统一通信数字化转型</h4>
                        <p class="text-gray-600 mb-4">通过数字化系统建设，实现生产过程可视化、配方管理智能化、质量追溯全程化，大幅提升运营效率。</p>
                        <a href="../cases/case5.html" class="text-blue-600 hover:text-blue-800">查看详情 →</a>
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    <!-- 咨询入口 -->
    <!-- <section class="py-16 bg-gray-800 text-white text-center">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold mb-6">开启您的行业数字化转型之旅</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">我们的专业团队将为您提供针对行业特点的定制化解决方案，助力您的企业实现降本增效、提质增产的目标。</p>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="../contact.html" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md font-medium transition duration-300">联系我们</a>
                <a href="../contact.html#demo" class="bg-white hover:bg-gray-100 text-blue-800 px-8 py-3 rounded-md font-medium transition duration-300">预约演示</a>
            </div>
        </div>
    </section> -->

        <!-- 咨询入口 -->
        <section class="py-16 bg-blue-800 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl font-bold mb-6">准备开启您的数字化转型之旅？</h2>
                <p class="max-w-3xl mx-auto mb-8 text-lg">我们的专业团队将为您提供一对一咨询，详细了解您的需求，并提供定制化解决方案。</p>
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <a href="contact.html#demo" class="bg-white text-blue-800 px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition duration-300">预约演示</a>
                    <a href="tel:+86051268666026" class="border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-blue-800 transition duration-300">立即咨询: 0512-68666026</a>
                </div>
            </div>
        </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="../js/components.js?v=1.0.1"></script>

    <script>
        // 页面加载完成后显示
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('loaded');
            
            // 检查URL中是否有锚点，如果有则切换到对应标签页
            if (window.location.hash) {
                const tabId = window.location.hash.substring(1);
                switchTab(tabId);
                
                // 延迟一点时间，确保DOM已完全加载
                setTimeout(() => {
                    // 找到对应的按钮并模拟点击
                    document.querySelectorAll('.tab-button').forEach(button => {
                        if (button.innerText.trim().includes(
                            tabId === 'auto' ? '汽车零部件' : 
                            (tabId === 'electronics' ? '电子制造' : '食品饮料'))) {
                            // 触发按钮点击事件，确保视觉状态一致
                            button.click();
                        }
                    });
                    
                    // 确保对应内容是可见的
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    const targetTab = document.getElementById(tabId);
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }
                }, 100);
            }
        });

        // 切换标签页
        function switchTab(tabId) {
            // 确保传入的标签ID有效
            if (!tabId || !['auto', 'electronics', 'food'].includes(tabId)) {
                tabId = 'auto'; // 默认显示汽车制造标签
            }
            
            // 更新按钮状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('bg-blue-600', 'text-white');
                button.classList.add('bg-blue-100', 'text-blue-800');
            });
            
            // 找到被点击的按钮并更新其样式
            document.querySelectorAll('.tab-button').forEach(button => {
                if (button.innerText.trim().includes(tabId === 'auto' ? '汽车零部件' : (tabId === 'electronics' ? '电子制造' : '食品饮料'))) {
                    button.classList.remove('bg-blue-100', 'text-blue-800');
                    button.classList.add('bg-blue-600', 'text-white');
                }
            });

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');
            
            // 更新URL，但不触发页面刷新
            history.replaceState(null, null, `#${tabId}`);
        }
    </script>
</body>
</html> 