<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧工厂解决方案 | 制造业数字化转型专家</title>
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/vendor/alpine.min.js" defer></script>
    <style>
        /* 直接在页面中添加背景图片，不依赖外部CSS */
        .hero-section {
            background-image: url('images/home/<USER>');
            background-size: cover;
            background-position: center;
            position: relative;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to right, rgba(30, 58, 138, 0.9), rgba(30, 64, 175, 0.8));
            z-index: 0;
        }
        
        .hero-section > * {
            position: relative;
            z-index: 1;
        }
        
        .factory-pattern {
            background-image: url('images/home/<USER>');
            background-size: 200px;
            background-repeat: repeat;
            opacity: 0.05;
        }
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }

        /* FAQ内容样式 */
        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .faq-content.active {
            max-height: 500px;
        }
    </style>
    <script>
        <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:5356023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>

</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 首屏 -->
    <section class="hero-section pt-24 md:pt-32 pb-16 bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-6">帮制造业企业降低15%生产浪费，实现透明化管控</h1>
                    <div class="mb-8">
                        <p class="flex items-center mb-3">
                            <svg class="h-5 w-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            适配离散制造及流程工业
                        </p>
                        <p class="flex items-center mb-3">
                            <svg class="h-5 w-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            与ERP/PLC无缝对接
                        </p>
                        <p class="flex items-center mb-3">
                            <svg class="h-5 w-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            可视化数据分析和决策支持
                        </p>
                        <p class="flex items-center">
                            <svg class="h-5 w-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            专业实施团队，快速落地
                        </p>
                    </div>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="contact.html#demo" class="bg-white text-blue-800 px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition duration-300 text-center">预约演示</a>
                        <!-- <a href="solutions.html#trial" class="border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-blue-800 transition duration-300 text-center">免费试用</a> -->
                    </div>
                </div>
                <div class="md:w-1/2">
                    <img src="images/home/<USER>" alt="工厂数字化管控平台" class="rounded-lg shadow-2xl">
                </div>
            </div>
        </div>
    </section>

    <!-- 客户展示 -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-center text-3xl font-bold mb-12">值得信赖的合作伙伴</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-16">
                <div class="flex items-center justify-center">
                    <img src="images/clients/siemens.png" alt="西门子" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
                <div class="flex items-center justify-center">
                    <img src="images/clients/bmw.png" alt="宝马" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
                <div class="flex items-center justify-center">
                    <img src="images/clients/haier.png" alt="海尔" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
                <div class="flex items-center justify-center">
                    <img src="images/clients/lenovo.png" alt="联想" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
                <div class="flex items-center justify-center">
                    <img src="images/clients/bosch.png" alt="博世" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
                <div class="flex items-center justify-center">
                    <img src="images/clients/foxconn.png" alt="富士康" class="h-12 grayscale hover:grayscale-0 transition duration-300">
                </div>
            </div>

            <h3 class="text-center text-2xl font-bold mb-10">客户成果展示</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                    <div class="text-blue-800 text-4xl font-bold mb-3">30%</div>
                    <h4 class="text-xl font-semibold mb-3">库存周转率提升</h4>
                    <p class="text-gray-600">某汽车零部件制造商通过我们的WMS系统优化了仓储管理流程，实现了库存周转率提升30%。</p>
                </div>
                <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                    <div class="text-blue-800 text-4xl font-bold mb-3">25%</div>
                    <h4 class="text-xl font-semibold mb-3">生产效率提升</h4>
                    <p class="text-gray-600">某家用电器制造企业利用我们的MES系统实现了生产过程透明化，生产效率提升25%，不良品率降低15%。</p>
                </div>
                <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                    <div class="text-blue-800 text-4xl font-bold mb-3">40%</div>
                    <h4 class="text-xl font-semibold mb-3">决策响应速度提升</h4>
                    <p class="text-gray-600">某电子制造企业通过我们的供产销一体化系统实现了全流程透明化，决策响应速度提升40%。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 解决方案预览 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-center text-3xl font-bold mb-12">核心解决方案</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/solutions/smart-factory.jpg" alt="智能工厂解决方案" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3">智能工厂解决方案</h3>
                        <p class="text-gray-600 mb-4">整合MES与设备联网技术，实现生产全流程可视化管理，提高生产效率和产品质量。</p>
                        <a href="solutions.html#smart-factory" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/solutions/lean-warehouse.jpg" alt="精益仓储管理" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3">精益仓储管理</h3>
                        <p class="text-gray-600 mb-4">WMS与智能物流设备联动，优化仓储布局与出入库流程，降低库存成本，加快周转速度。</p>
                        <a href="solutions.html#lean-warehouse" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/solutions/OrderToDelievery.jpg" alt="供产销一体化管理系统" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3">供产销一体化管理</h3>
                        <p class="text-gray-600 mb-4">打通供应链、生产与销售环节，实现跨部门协同，优化计划排程，提高资源利用率。</p>
                        <a href="solutions.html#integrated-management" class="text-blue-800 font-medium hover:underline">了解更多 →</a>
                    </div>
                </div>
            </div>
            <div class="text-center mt-10">
                <a href="solutions.html" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">查看全部解决方案</a>
            </div>
        </div>
    </section>

    <!-- 咨询入口 -->
    <section class="py-16 bg-blue-800 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">准备开启您的数字化转型之旅？</h2>
            <p class="max-w-3xl mx-auto mb-8 text-lg">我们的专业团队将为您提供一对一咨询，详细了解您的需求，并提供定制化解决方案。</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="contact.html#demo" class="bg-white text-blue-800 px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition duration-300">预约演示</a>
                <a href="tel:+86051268666026" class="border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-blue-800 transition duration-300">立即咨询: 0512-68666026</a>
            </div>
        </div>
    </section>

    <!-- 预约演示 -->
    <section id="demo" class="py-16 bg-blue-50">
        <!-- 预约演示表单处理 -->
    </section>

    <!-- FAQ Section -->
    <section class="container mx-auto px-4 py-16 mb-16">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">常见问题</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">以下是我们客户经常询问的问题。如果您有其他疑问，请随时通过联系表单或客服热线与我们联系。</p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="mb-5">
                <button
                    class="flex justify-between items-center w-full bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border-l-4 border-blue-600"
                    onclick="toggleFaq(this)"
                >
                    <h3 class="text-xl font-semibold text-gray-800">您的解决方案适用于哪些行业？</h3>
                    <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content">
                    <div class="bg-white p-6 rounded-b-lg shadow-md">
                        <p class="text-gray-600">我们的智能制造解决方案适用于多个行业，包括但不限于：汽车制造、电子电器、医药制造、食品饮料、机械装备、航空航天等。我们的系统可根据各行业特点进行定制化配置，满足不同行业的特殊需求。</p>
                    </div>
                </div>
            </div>
            
            <div class="mb-5">
                <button
                    class="flex justify-between items-center w-full bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border-l-4 border-blue-600"
                    onclick="toggleFaq(this)"
                >
                    <h3 class="text-xl font-semibold text-gray-800">实施周期通常需要多长时间？</h3>
                    <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content">
                    <div class="bg-white p-6 rounded-b-lg shadow-md">
                        <p class="text-gray-600">实施周期取决于项目规模和复杂度。一般情况下，中小型项目通常需要2-3个月完成从需求分析到系统上线的全过程；大型项目可能需要4-6个月。我们采用敏捷开发方法，可以分阶段实施，让您尽快看到成效。</p>
                    </div>
                </div>
            </div>
            
            <div class="mb-5">
                <button
                    class="flex justify-between items-center w-full bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border-l-4 border-blue-600"
                    onclick="toggleFaq(this)"
                >
                    <h3 class="text-xl font-semibold text-gray-800">您的解决方案是否支持与现有系统集成？</h3>
                    <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content">
                    <div class="bg-white p-6 rounded-b-lg shadow-md">
                        <p class="text-gray-600">是的，我们的解决方案设计具有高度的兼容性和灵活性。我们支持与大多数主流ERP系统（如SAP、Oracle、金蝶、用友等）、PLM系统、SCM系统以及各类自动化设备和传感器进行集成。我们提供标准API和接口，确保数据能够无缝流通。</p>
                    </div>
                </div>
            </div>
            
            <div class="mb-5">
                <button
                    class="flex justify-between items-center w-full bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border-l-4 border-blue-600"
                    onclick="toggleFaq(this)"
                >
                    <h3 class="text-xl font-semibold text-gray-800">如何确保系统的安全性？</h3>
                    <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content">
                    <div class="bg-white p-6 rounded-b-lg shadow-md">
                        <p class="text-gray-600">我们高度重视系统安全。我们的解决方案采用多层次安全架构，包括数据加密、安全认证、权限管理、操作审计等措施。我们遵循ISO 27001信息安全管理标准，并定期进行安全评估和漏洞修复。对于关键数据，我们提供备份和灾难恢复解决方案，确保业务连续性。</p>
                    </div>
                </div>
            </div>
            
            <div class="mb-5">
                <button
                    class="flex justify-between items-center w-full bg-white p-6 rounded-lg shadow hover:shadow-md transition-all border-l-4 border-blue-600"
                    onclick="toggleFaq(this)"
                >
                    <h3 class="text-xl font-semibold text-gray-800">实施后提供哪些支持和服务？</h3>
                    <svg class="w-6 h-6 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content">
                    <div class="bg-white p-6 rounded-b-lg shadow-md">
                        <p class="text-gray-600">我们提供全面的实施后支持服务，包括：</p>
                        <ul class="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>7x24小时技术支持热线</li>
                            <li>定期系统健康检查</li>
                            <li>软件版本升级</li>
                            <li>用户培训和知识转移</li>
                            <li>性能优化建议</li>
                            <li>根据业务变化的系统调整</li>
                        </ul>
                        <p class="mt-2 text-gray-600">我们提供不同级别的服务合同，您可以根据需求选择适合的服务方案。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="js/components.js?v=1.0.1"></script>

    <!-- 表单提交处理 -->
    <script>
        // 预约演示表单处理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('demo-form');
            if (form) {
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    // 表单提交逻辑
                });
            }
        });

        function toggleFaq(button) {
            // 获取当前按钮下的内容区域
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');
            
            // 关闭其他所有打开的内容
            document.querySelectorAll('.faq-content').forEach(item => {
                if (item !== content) {
                    item.classList.remove('active');
                }
            });
            
            // 切换当前内容
            content.classList.toggle('active');
            
            // 旋转图标
            icon.style.transform = content.classList.contains('active') ? 'rotate(180deg)' : '';
        }
    </script>
</body>
</html> 