/* 主题颜色 */
:root {
    --primary-blue: #1e40af;
    --dark-blue: #1e3a8a;
    --light-blue: #3b82f6;
    --dark-gray: #374151;
    --medium-gray: #6b7280;
    --light-gray: #f3f4f6;
}

/* 全局元素 */
body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    color: var(--dark-gray);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
}

a {
    transition: all 0.3s ease;
}

/* 自定义组件 */
.stat-card {
    border-left: 4px solid var(--primary-blue);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.case-card {
    transition: all 0.3s ease;
}

.case-card:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-5px);
}

/* 表单样式 */
.form-input {
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    width: 100%;
    transition: border-color 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: var(--dark-blue);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary {
    border: 2px solid var(--primary-blue);
    color: var(--primary-blue);
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-blue);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 数据动画效果 */
.count-up {
    font-weight: 700;
    color: var(--primary-blue);
}

/* 工业仪表盘样式 */
.dashboard-container {
    background-color: #1a1a1a;
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid #333;
}

.dashboard-value {
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    color: #3aff3a;
    background-color: #000;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
}

/* 聊天机器人 */
.chat-bubble {
    position: absolute;
    bottom: 80px;
    right: 20px;
    width: 300px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 100;
    overflow: hidden;
    display: none;
}

.chat-header {
    background: var(--primary-blue);
    color: white;
    padding: 15px;
    font-weight: bold;
}

.chat-messages {
    height: 300px;
    padding: 15px;
    overflow-y: auto;
}

.chat-input {
    border-top: 1px solid #eee;
    padding: 10px;
    display: flex;
}

.chat-input input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 15px;
    outline: none;
}

.chat-input button {
    background: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 15px;
    margin-left: 10px;
    cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 6rem;
        padding-bottom: 3rem;
    }
    
    .chat-bubble {
        width: 85vw;
        right: 50%;
        transform: translateX(50%);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease forwards;
}

/* 锚点定位调整 - 解决导航栏遮挡问题 */
section[id] {
    scroll-margin-top: 120px; /* 为锚点添加顶部边距，避免被导航栏遮挡 */
}

/* 解决方案图片统一高度 */
.solution-image {
    height: 400px; /* 设定统一高度 */
    width: 100%;
    object-fit: cover; /* 确保图片按比例缩放并填充容器 */
    object-position: center; /* 保持图片居中 */
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
    }
    
    .container {
        width: 100%;
        max-width: none;
    }
} 