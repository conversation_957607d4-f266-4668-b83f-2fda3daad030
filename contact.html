<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 | 制造业数字化转型专家</title>
    <meta name="description" content="与我们取得联系，了解智能制造解决方案详情，预约产品演示或咨询技术支持。">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
    </style>
        <script>
        <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:5356023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 页面标题 -->
    <section class="bg-blue-800 pt-24 pb-12 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-4">联系我们</h1>
            <p class="text-xl max-w-3xl">无论您有任何问题或需求，我们的专业团队随时为您提供支持与解答。请通过以下方式与我们取得联系。</p>
        </div>
    </section>

    <!-- 联系信息 -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row -mx-4">
                <!-- 联系方式 -->
                <div class="lg:w-1/3 px-4 mb-10 lg:mb-0">
                    <div class="bg-white rounded-lg shadow-md p-8 h-full">
                        <h2 class="text-2xl font-bold mb-6">联系方式</h2>
                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-3 mr-4">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold mb-1">公司地址</h3>
                                    <p class="text-gray-600">江苏省苏州市工业园区林泉街399号东南大学成贤院304、306</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-3 mr-4">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold mb-1">电话咨询</h3>
                                    <p class="text-gray-600">0512-68666026</p>
                                    <!-- <p class="text-gray-600">021-5050-5050</p> -->
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-3 mr-4">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold mb-1">电子邮箱</h3>
                                    <p class="text-gray-600"><EMAIL></p>
                                    <!-- <p class="text-gray-600"><EMAIL></p> -->
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-3 mr-4">
                                    <svg class="h-6 w-6 text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold mb-1">工作时间</h3>
                                    <p class="text-gray-600">周一至周五：9:00 - 18:00</p>
                                    <p class="text-gray-600">周六、周日：休息</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold mb-3">关注我们</h3>
                            <div class="flex space-x-4">
                                <div class="text-center">
                                    <img src="images/qrcode.jpg" alt="微信公众号二维码" class="w-32 h-32 object-contain mb-2">
                                    <p class="text-sm text-gray-600">扫码关注公众号</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 联系表单 -->
                <div class="lg:w-2/3 px-4">
                    <div class="bg-white rounded-lg shadow-md p-8 h-full">
                        <h2 class="text-2xl font-bold mb-6">给我们留言</h2>
                        <form id="contact-form" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-gray-700 font-medium mb-2">您的姓名 <span class="text-red-500">*</span></label>
                                    <input type="text" id="name" name="name" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label for="company" class="block text-gray-700 font-medium mb-2">公司名称 <span class="text-red-500">*</span></label>
                                    <input type="text" id="company" name="company" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-700 font-medium mb-2">电子邮箱 <span class="text-red-500">*</span></label>
                                    <input type="email" id="email" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label for="phone" class="block text-gray-700 font-medium mb-2">联系电话 <span class="text-red-500">*</span></label>
                                    <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                            </div>
                            
                            <div>
                                <label for="subject" class="block text-gray-700 font-medium mb-2">咨询主题</label>
                                <select id="subject" name="subject" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择咨询主题</option>
                                    <option value="MES">制造执行系统(MES)</option>
                                    <option value="WMS">仓储管理系统(WMS)</option>
                                    <option value="OTD">供产销一体化系统(OTD)</option>
                                    <!-- <option value="QMS">质量管理系统(QMS)</option> -->
                                    <option value="DMS">设备管理系统(DMS)</option>
                                    <!-- <option value="APM">高级生产计划(APM)</option> -->
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="message" class="block text-gray-700 font-medium mb-2">留言内容 <span class="text-red-500">*</span></label>
                                <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="agree" name="agree" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                                <label for="agree" class="ml-2 block text-gray-700">我同意贵公司根据<a href="#" class="text-blue-600 hover:underline">隐私政策</a>处理我的个人信息</label>
                            </div>
                            
                            <div>
                                <button type="submit" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">提交信息</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="bg-gray-50 py-12 mb-16">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-6">我们的位置</h2>
                    <p class="text-gray-600 mb-6">我们位于江苏省苏州市工业园区，距离苏州园区高铁站约30分钟车程。欢迎您来访并了解我们的智能制造解决方案。</p>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="font-bold text-xl mb-4 text-primary-600">公司地址</h3>
                        <p class="mb-2">江苏省苏州市工业园区林泉街399号东南大学成贤院304、306 </p>
                        <h3 class="font-bold text-xl mb-4 mt-6 text-primary-600">乘车路线</h3>
                        <p class="mb-2"><span class="font-bold">地铁：</span>地铁2号线松涛街站下车，2号出口步行10分钟</p>
                        <p class="mb-2"><span class="font-bold">公交：</span>文荟广场西站下车，步行5分钟</p>
                    </div>
                </div>
                <div class="relative h-96 rounded-lg overflow-hidden shadow-xl">
                    <img src="/images/resources/map.jpg"
                         alt="公司位置地图" class="w-full h-full object-cover">
                    <div class="absolute bottom-4 right-4">
                        <a href="https://j.map.baidu.com/3d/Abc" target="_blank" class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 inline-flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            获取导航
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 预约演示 -->
    <section id="demo" class="py-16 bg-blue-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold mb-8 text-center">预约产品演示</h2>
                <p class="text-gray-600 text-center mb-10">通过预约我们的产品演示，您可以更直观地了解我们的解决方案如何帮助您的企业提高效率、降低成本，实现数字化转型。</p>
                
                <div class="bg-white rounded-lg shadow-md p-8">
                    <form id="demo-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="demo-name" class="block text-gray-700 font-medium mb-2">您的姓名 <span class="text-red-500">*</span></label>
                                <input type="text" id="demo-name" name="demo-name" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="demo-company" class="block text-gray-700 font-medium mb-2">公司名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="demo-company" name="demo-company" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="demo-email" class="block text-gray-700 font-medium mb-2">电子邮箱 <span class="text-red-500">*</span></label>
                                <input type="email" id="demo-email" name="demo-email" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label for="demo-phone" class="block text-gray-700 font-medium mb-2">联系电话 <span class="text-red-500">*</span></label>
                                <input type="tel" id="demo-phone" name="demo-phone" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                        
                        <div>
                            <label for="demo-product" class="block text-gray-700 font-medium mb-2">演示产品 <span class="text-red-500">*</span></label>
                            <select id="demo-product" name="demo-product" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择需要演示的产品</option>
                                <option value="MES">制造执行系统(MES)</option>
                                <option value="WMS">仓储管理系统(WMS)</option>
                                <option value="OTD">供产销一体化系统(OTD)</option>
                                <!-- <option value="QMS">质量管理系统(QMS)</option> -->
                                <option value="DMS">设备管理系统(DMS)</option>
                                <!-- <option value="APM">高级生产计划(APM)</option> -->
                                <option value="全部">全套解决方案</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="demo-date" class="block text-gray-700 font-medium mb-2">期望演示日期 <span class="text-red-500">*</span></label>
                            <input type="date" id="demo-date" name="demo-date" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        
                        <div>
                            <label for="demo-notes" class="block text-gray-700 font-medium mb-2">其他需求或说明</label>
                            <textarea id="demo-notes" name="demo-notes" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="demo-agree" name="demo-agree" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                            <label for="demo-agree" class="ml-2 block text-gray-700">我同意贵公司根据<a href="#" class="text-blue-600 hover:underline">隐私政策</a>处理我的个人信息</label>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="inline-block bg-blue-800 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition duration-300">提交预约</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="js/components.js?v=1.0.1"></script>

    <!-- 表单提交处理 -->
    <script>
        // 预约演示表单处理
        document.getElementById('demo-form').addEventListener('submit', async function(e) {
            e.preventDefault();
               // 获取下拉框的显示文本
            const subjectSelect = document.getElementById('demo-product');
            const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
            const subjectText = selectedOption ? selectedOption.text : '';
            
            // 获取表单数据
            const formData = {
                appToken: "LUiCbXfBbaTeUpsjMlXcEIHhnkc",
                tableId: "tblzQRCgiMkYywsf",
                fields: {
                    "姓名": document.getElementById('demo-name').value,
                    "公司名称": document.getElementById('demo-company').value,
                    "电子邮箱": document.getElementById('demo-email').value,
                    "联系电话": document.getElementById('demo-phone').value,
                    "演示产品": subjectText,
                    "期望演示日期": new Date(document.getElementById('demo-date').value).getTime(),
                    "其他需求或说明": document.getElementById('demo-notes').value
                }
            };

            try {
                const response = await fetch('https://app.otd-odincloud.com/api/website/record', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    alert('预约提交成功！我们会尽快与您联系。');
                    this.reset();
                } else {
                    throw new Error('提交失败');
                }
            } catch (error) {
                alert('抱歉，提交失败。请稍后重试或直接联系我们。');
                console.error('提交错误:', error);
            }
        });

        // 联系我们表单处理
        document.getElementById('contact-form').addEventListener('submit', async function(e) {
            e.preventDefault();
                     
            // 获取下拉框的显示文本
            const subjectSelect = document.getElementById('subject');
            const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
            const subjectText = selectedOption ? selectedOption.text : '';
            
            // 获取表单数据
            const formData = {
                appToken: "SIW0bWSbBaO2Ucsa9g5cz2TMnbe",
                tableId: "tblLRlPsvQY8c8qb",
                fields: {
                    "姓名": document.getElementById('name').value,
                    "公司名称": document.getElementById('company').value,
                    "电子邮箱": document.getElementById('email').value,
                    "联系电话": document.getElementById('phone').value,
                    "咨询主题": subjectText,
                    "留言内容": document.getElementById('message').value
                }
            };

            try {
                const response = await fetch('https://app.otd-odincloud.com/api/website/record', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    alert('留言提交成功！我们会尽快与您联系。');
                    this.reset();
                } else {
                    throw new Error('提交失败');
                }
            } catch (error) {
                alert('抱歉，提交失败。请稍后重试或直接联系我们。');
                console.error('提交错误:', error);
            }
        });
    </script>
</body>
</html> 