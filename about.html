<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 | 制造业数字化转型专家</title>
    <meta name="description" content="了解我们公司的发展历程、团队和企业文化，探索我们为制造业提供的智能解决方案。">
    <link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script defer src="js/vendor/alpine.min.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* 页面加载过渡样式 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
    </style>
        <script>
        <!-- Hotjar Tracking Code for Site 5356023 (name missing) -->

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:5356023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body class="font-sans text-gray-800 bg-gray-50 page-loading">
    <!-- 页头容器 -->
    <div id="header-container"></div>

    <!-- 主标题 -->
    <section class="bg-blue-800 pt-24 pb-12 text-white">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-4">关于我们</h1>
            <p class="text-xl max-w-3xl">我们是智能制造解决方案的领导者，致力于帮助制造企业实现数字化转型与效率提升</p>
        </div>
    </section>

    <!-- 公司介绍 -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center gap-8 md:gap-12 lg:gap-16">
                <div class="md:w-1/2 mb-8 md:mb-0">
                    <h2 class="text-3xl font-bold mb-6">企业简介</h2>
                    <p class="text-gray-600 mb-4 text-lg">奥斯坦丁软件科技成立于2017年，是专注于为制造企业提供数字化转型解决方案的高新技术企业。我们汇集了一支拥有深厚制造业经验和信息技术背景的精英团队，致力于通过先进的软件技术帮助制造企业提升生产效率、降低运营成本、加强质量控制。</p>
                    <p class="text-gray-600 mb-6 text-lg">八年来，我们已为40多家制造企业提供了MES、WMS、OTD等系统实施服务，客户遍布汽车、电子、注塑、食品等多个行业。</p>
                    <div class="flex flex-wrap gap-4 mb-6">
                        <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-medium">高新技术企业</div>
                        <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-medium">CMMI3级认证</div>
                        <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-medium">40+软件著作权</div>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="rounded-lg shadow-xl overflow-hidden">
                        <img src="images/about/office.jpg" alt="公司办公环境" class="w-full h-64 object-cover">
                    </div>
                </div>
            </div>

            <!-- 公司数据 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-20">
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="text-3xl font-bold text-blue-800 mb-2 count-up" data-target="40">40+</div>
                    <p class="text-gray-600">企业客户</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="text-3xl font-bold text-blue-800 mb-2 count-up" data-target="30">30</div>
                    <p class="text-gray-600">核心团队</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="text-3xl font-bold text-blue-800 mb-2 count-up" data-target="8">8</div>
                    <p class="text-gray-600">行业经验</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="text-3xl font-bold text-blue-800 mb-2 count-up" data-target="40">40+</div>
                    <p class="text-gray-600">软件著作权</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 发展历程 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-800">发展历程</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">从初创到行业领导者，我们坚持以客户为中心，不断创新</p>
            </div>
            
            <div class="relative">
                <!-- 中心线 -->
                <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-blue-200"></div>
                
                <!-- 2017年 -->
                <div class="relative mb-16">
                    <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-4">
                        <div class="bg-blue-800 rounded-full h-8 w-8 flex items-center justify-center shadow-md">
                            <span class="text-white font-bold">1</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-8">
                        <div class="text-right pr-8">
                            <h3 class="text-xl font-bold text-blue-800 mb-2">2017年</h3>
                            <h4 class="font-bold text-gray-800 mb-2">公司成立</h4>
                            <p class="text-gray-600">创始团队3人，推出首个MES产品</p>
                        </div>
                        <div>
                            <img src="images/about/founding.jpg" alt="公司成立" class="rounded-lg shadow-md w-full h-64 object-cover">
                        </div>
                    </div>
                </div>
                
                <!-- 2019年 -->
                <div class="relative mb-16">
                    <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-4">
                        <div class="bg-blue-800 rounded-full h-8 w-8 flex items-center justify-center shadow-md">
                            <span class="text-white font-bold">2</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-8">
                        <div>
                            <img src="images/about/funding.jpg" alt="承接项目" class="rounded-lg shadow-md w-full h-64 object-cover">
                        </div>
                        <div class="pl-8">
                            <h3 class="text-xl font-bold text-blue-800 mb-2">2019年</h3>
                            <h4 class="font-bold text-gray-800 mb-2">承接小米亦庄黑灯工厂项目</h4>
                            <p class="text-gray-600">经过1年时间，成功交付小米亦庄黑灯工厂项目，翌年，小米黑灯工厂在央视多频道报道，获得广泛关注</p>
                        </div>
                    </div>
                </div>
                
                <!-- 2020年 -->
                <div class="relative mb-16">
                    <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-4">
                        <div class="bg-blue-800 rounded-full h-8 w-8 flex items-center justify-center shadow-md">
                            <span class="text-white font-bold">3</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-8">
                        <div class="text-right pr-8">
                            <h3 class="text-xl font-bold text-blue-800 mb-2">2020年</h3>
                            <h4 class="font-bold text-gray-800 mb-2">全面解决方案</h4>
                            <p class="text-gray-600">推出智能工厂全面解决方案，包括WMS、OTD、数字孪生等，成功交付包括伟创力、夏普在内的众多500强客户</p>
                        </div>
                        <div>
                            <img src="images/about/solutions.jpg" alt="全面解决方案" class="rounded-lg shadow-md">
                        </div>
                    </div>
                </div>
                
                <!-- 2022年 -->
                <div class="relative">
                    <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-4">
                        <div class="bg-blue-800 rounded-full h-8 w-8 flex items-center justify-center shadow-md">
                            <span class="text-white font-bold">4</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-8">
                        <div>
                            <img src="images/about/leader.jpg" alt="持续服务者" class="rounded-lg shadow-md w-full h-64 object-cover">
                        </div>
                        <div class="pl-8">
                            <h3 class="text-xl font-bold text-blue-800 mb-2">2022年至今</h3>
                            <h4 class="font-bold text-gray-800 mb-2">工业服务践行者</h4>
                            <p class="text-gray-600">继续深耕，为制造业客户持续提供更优质的服务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心团队 -->
    <!-- <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4 text-gray-800">核心团队</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">我们的团队汇集了制造业和IT领域的精英，拥有深厚的行业经验和技术背景</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/about/team1.jpg" alt="张明 - CEO" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-1">张明</h3>
                        <p class="text-blue-800 font-medium mb-3">创始人兼CEO</p>
                        <p class="text-gray-600 mb-4">前西门子高级工程师，拥有15年制造业信息化经验，多项MES相关专利持有人。</p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-twitter"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/about/team2.jpg" alt="李婷 - CTO" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-1">李婷</h3>
                        <p class="text-blue-800 font-medium mb-3">CTO</p>
                        <p class="text-gray-600 mb-4">前Google高级工程师，人工智能和大数据专家，负责公司产品技术架构和创新研发。</p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-github"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/about/team3.jpg" alt="王强 - COO" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-1">王强</h3>
                        <p class="text-blue-800 font-medium mb-3">COO</p>
                        <p class="text-gray-600 mb-4">前联想供应链总监，拥有20年制造业运营经验，精通精益生产和六西格玛管理。</p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-weibo"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
               
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <img src="images/about/team4.jpg" alt="赵芳 - CMO" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-1">赵芳</h3>
                        <p class="text-blue-800 font-medium mb-3">CMO</p>
                        <p class="text-gray-600 mb-4">前SAP大中华区市场总监，擅长B2B市场策略和品牌建设，推动公司市场份额持续扩大。</p>
                        <div class="flex space-x-3">
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-blue-800">
                                <i class="fab fa-twitter"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- 企业文化 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4 text-gray-800">企业文化</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">我们的企业文化塑造了我们的工作方式和价值观念，指引我们为客户创造更大价值</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 企业文化1 -->
                <div class="bg-white p-8 rounded-lg shadow-md border-t-4 border-blue-800">
                    <div class="text-blue-800 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">持续创新</h3>
                    <p class="text-gray-600">我们鼓励团队不断探索新技术、新方法，挑战传统制造模式，为客户提供创新解决方案，引领行业发展趋势。</p>
                </div>
                
                <!-- 企业文化2 -->
                <div class="bg-white p-8 rounded-lg shadow-md border-t-4 border-blue-800">
                    <div class="text-blue-800 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">客户至上</h3>
                    <p class="text-gray-600">我们始终将客户需求放在首位，深入理解客户业务痛点，提供贴合实际的解决方案，建立长期信任合作关系。</p>
                </div>
                
                <!-- 企业文化3 -->
                <div class="bg-white p-8 rounded-lg shadow-md border-t-4 border-blue-800">
                    <div class="text-blue-800 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-4">专业卓越</h3>
                    <p class="text-gray-600">我们追求技术和服务的卓越标准，每位团队成员都保持专业素养和持续学习，确保交付高质量的产品和服务。</p>
                </div>
            </div>
            
            <div class="mt-12 bg-white p-8 rounded-lg shadow-md">
                <h3 class="text-2xl font-bold mb-6 text-center">我们的价值观</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="flex flex-col items-center text-center">
                        <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <span class="text-2xl font-bold text-blue-800">诚</span>
                        </div>
                        <h4 class="font-bold mb-2">诚信</h4>
                        <p class="text-gray-600">诚实守信，言行一致</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <span class="text-2xl font-bold text-blue-800">创</span>
                        </div>
                        <h4 class="font-bold mb-2">创新</h4>
                        <p class="text-gray-600">勇于创新，引领未来</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <span class="text-2xl font-bold text-blue-800">协</span>
                        </div>
                        <h4 class="font-bold mb-2">协作</h4>
                        <p class="text-gray-600">团队协作，共创佳绩</p>
                    </div>
                    <div class="flex flex-col items-center text-center">
                        <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <span class="text-2xl font-bold text-blue-800">精</span>
                        </div>
                        <h4 class="font-bold mb-2">精益</h4>
                        <p class="text-gray-600">精益求精，追求卓越</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 荣誉资质 -->
    <section class="py-16 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-800">荣誉资质</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">我们的成就和行业认可</p>
            </div>
            
            <!-- 横向图片 -->
            <div class="grid grid-cols-3 gap-8 mb-12">
                <div class="p-4 bg-white rounded-lg shadow-md text-center">
                    <div class="aspect-w-16 aspect-h-9 mb-4">
                        <img src="images/about/gaoqi.jpg" alt="高新技术企业" class="object-contain w-full h-full">
                    </div>
                    <h3 class="font-bold">高新技术企业</h3>
                </div>
                <div class="p-4 bg-white rounded-lg shadow-md text-center">
                    <div class="aspect-w-16 aspect-h-9 mb-4">
                        <img src="images/about/CMMI.jpg" alt="CMMI3级认证" class="object-contain w-full h-full">
                    </div>
                    <h3 class="font-bold">CMMI3级认证</h3>
                </div>
                <div class="p-4 bg-white rounded-lg shadow-md text-center">
                    <div class="aspect-w-3 aspect-h-4 mb-4">
                        <img src="images/about/minke.jpg" alt="民营科技型企业" class="object-contain w-full h-full">
                    </div>
                    <h3 class="font-bold">民营科技型企业</h3>
                </div>
            </div>

            <!-- 纵向图片 -->
            <div class="flex justify-center">
                <div class="grid grid-cols-2 gap-8 w-1/2">
                    <div class="p-4 bg-white rounded-lg shadow-md text-center">
                        <div class="aspect-w-3 aspect-h-4 mb-4">
                            <img src="images/about/AAA.jpg" alt="质量AAA认证" class="object-contain w-full h-full">
                        </div>
                        <h3 class="font-bold">质量AAA认证</h3>
                    </div>
                    <div class="p-4 bg-white rounded-lg shadow-md text-center">
                        <div class="aspect-w-16 aspect-h-9 mb-4">
                            <img src="images/about/ISO9001CN.jpg" alt="ISO9001认证" class="object-contain w-full h-full">
                        </div>
                        <h3 class="font-bold">ISO9001认证</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 合作伙伴 -->
    <!-- <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4 text-gray-800">合作伙伴</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">我们与行业领先企业携手共进，共同创造价值</p>
            </div>
            
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8">
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+1" alt="合作伙伴1">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+2" alt="合作伙伴2">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+3" alt="合作伙伴3">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+4" alt="合作伙伴4">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+5" alt="合作伙伴5">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+6" alt="合作伙伴6">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+7" alt="合作伙伴7">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+8" alt="合作伙伴8">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+9" alt="合作伙伴9">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+10" alt="合作伙伴10">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+11" alt="合作伙伴11">
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                    <img src="https://via.placeholder.com/120x60?text=Partner+12" alt="合作伙伴12">
                </div>
            </div>
        </div>
    </section> -->

    <!-- 预约演示 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">开启您的智能制造之旅</h2>
            <p class="text-gray-600 mb-8 max-w-2xl mx-auto">通过我们的解决方案，提升生产效率，降低运营成本，加速数字化转型</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="contact.html" class="bg-blue-800 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium">联系我们</a>
                <a href="contact.html#demo" class="bg-white text-blue-800 border border-blue-800 px-6 py-3 rounded-md hover:bg-blue-50 transition-colors font-medium">预约演示</a>
            </div>
        </div>
    </section>

    <!-- 页脚容器 -->
    <div id="footer-container"></div>

    <!-- 组件加载脚本 -->
    <script src="js/components.js?v=1.0.1"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        // 团队成员悬停效果
        document.querySelectorAll('.team-member').forEach(member => {
            member.addEventListener('mouseenter', () => {
                member.classList.add('transform', 'scale-105', 'shadow-lg');
                member.classList.remove('shadow-md');
            });
            member.addEventListener('mouseleave', () => {
                member.classList.remove('transform', 'scale-105', 'shadow-lg');
                member.classList.add('shadow-md');
            });
        });
        
        // 数字计数动画
        const countElements = document.querySelectorAll('.count-up');
        
        function animateCount(el) {
            const target = parseInt(el.getAttribute('data-target'));
            const duration = 2000; // 动画持续时间（毫秒）
            const frameRate = 50; // 每秒帧数
            const totalFrames = duration * frameRate / 1000;
            let frame = 0;
            
            const counter = setInterval(() => {
                frame++;
                const progress = frame / totalFrames;
                const currentCount = Math.round(progress * target);
                
                if (frame === totalFrames) {
                    clearInterval(counter);
                    el.textContent = target;
                } else {
                    el.textContent = currentCount;
                }
            }, 1000 / frameRate);
        }
        
        // 检测元素是否在视口中
        function isInViewport(el) {
            const rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
        
        // 滚动时检测并触发动画
        function checkCounters() {
            countElements.forEach(el => {
                if (isInViewport(el) && !el.classList.contains('counted')) {
                    el.classList.add('counted');
                    animateCount(el);
                }
            });
        }
        
        // 页面加载和滚动时检测
        window.addEventListener('load', checkCounters);
        window.addEventListener('scroll', checkCounters);
    </script>

</body>
</html> 